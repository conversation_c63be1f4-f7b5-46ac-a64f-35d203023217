"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\\n@layer properties;\\n@layer theme, base, components, utilities;\\n@layer theme {\\n  :root, :host {\\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-white: #fff;\\n    --spacing: 0.25rem;\\n    --container-2xl: 42rem;\\n    --container-4xl: 56rem;\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --radius-lg: 0.5rem;\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: var(--font-sans);\\n    --default-mono-font-family: var(--font-mono);\\n  }\\n}\\n@layer base {\\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\\n    box-sizing: border-box;\\n    margin: 0;\\n    padding: 0;\\n    border: 0 solid;\\n  }\\n  html, :host {\\n    line-height: 1.5;\\n    -webkit-text-size-adjust: 100%;\\n    tab-size: 4;\\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\");\\n    font-feature-settings: var(--default-font-feature-settings, normal);\\n    font-variation-settings: var(--default-font-variation-settings, normal);\\n    -webkit-tap-highlight-color: transparent;\\n  }\\n  hr {\\n    height: 0;\\n    color: inherit;\\n    border-top-width: 1px;\\n  }\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n  h1, h2, h3, h4, h5, h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n  b, strong {\\n    font-weight: bolder;\\n  }\\n  code, kbd, samp, pre {\\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace);\\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\\n    font-size: 1em;\\n  }\\n  small {\\n    font-size: 80%;\\n  }\\n  sub, sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n  sub {\\n    bottom: -0.25em;\\n  }\\n  sup {\\n    top: -0.5em;\\n  }\\n  table {\\n    text-indent: 0;\\n    border-color: inherit;\\n    border-collapse: collapse;\\n  }\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n  progress {\\n    vertical-align: baseline;\\n  }\\n  summary {\\n    display: list-item;\\n  }\\n  ol, ul, menu {\\n    list-style: none;\\n  }\\n  img, svg, video, canvas, audio, iframe, embed, object {\\n    display: block;\\n    vertical-align: middle;\\n  }\\n  img, video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n  button, input, select, optgroup, textarea, ::file-selector-button {\\n    font: inherit;\\n    font-feature-settings: inherit;\\n    font-variation-settings: inherit;\\n    letter-spacing: inherit;\\n    color: inherit;\\n    border-radius: 0;\\n    background-color: transparent;\\n    opacity: 1;\\n  }\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\\n    ::placeholder {\\n      color: currentcolor;\\n      @supports (color: color-mix(in lab, red, red)) {\\n        color: color-mix(in oklab, currentcolor 50%, transparent);\\n      }\\n    }\\n  }\\n  textarea {\\n    resize: vertical;\\n  }\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh;\\n    text-align: inherit;\\n  }\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n  button, input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]), ::file-selector-button {\\n    appearance: button;\\n  }\\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n@layer utilities {\\n  .fixed {\\n    position: fixed;\\n  }\\n  .top-0 {\\n    top: calc(var(--spacing) * 0);\\n  }\\n  .z-50 {\\n    z-index: 50;\\n  }\\n  .container {\\n    width: 100%;\\n    @media (width >= 40rem) {\\n      max-width: 40rem;\\n    }\\n    @media (width >= 48rem) {\\n      max-width: 48rem;\\n    }\\n    @media (width >= 64rem) {\\n      max-width: 64rem;\\n    }\\n    @media (width >= 80rem) {\\n      max-width: 80rem;\\n    }\\n    @media (width >= 96rem) {\\n      max-width: 96rem;\\n    }\\n  }\\n  .mx-auto {\\n    margin-inline: auto;\\n  }\\n  .mt-1 {\\n    margin-top: calc(var(--spacing) * 1);\\n  }\\n  .mr-4 {\\n    margin-right: calc(var(--spacing) * 4);\\n  }\\n  .mr-auto {\\n    margin-right: auto;\\n  }\\n  .mb-2 {\\n    margin-bottom: calc(var(--spacing) * 2);\\n  }\\n  .mb-3 {\\n    margin-bottom: calc(var(--spacing) * 3);\\n  }\\n  .mb-4 {\\n    margin-bottom: calc(var(--spacing) * 4);\\n  }\\n  .mb-6 {\\n    margin-bottom: calc(var(--spacing) * 6);\\n  }\\n  .mb-8 {\\n    margin-bottom: calc(var(--spacing) * 8);\\n  }\\n  .ml-2 {\\n    margin-left: calc(var(--spacing) * 2);\\n  }\\n  .flex {\\n    display: flex;\\n  }\\n  .grid {\\n    display: grid;\\n  }\\n  .h-2 {\\n    height: calc(var(--spacing) * 2);\\n  }\\n  .min-h-screen {\\n    min-height: 100vh;\\n  }\\n  .w-2 {\\n    width: calc(var(--spacing) * 2);\\n  }\\n  .w-full {\\n    width: 100%;\\n  }\\n  .max-w-2xl {\\n    max-width: var(--container-2xl);\\n  }\\n  .max-w-4xl {\\n    max-width: var(--container-4xl);\\n  }\\n  .flex-1 {\\n    flex: 1;\\n  }\\n  .grid-cols-1 {\\n    grid-template-columns: repeat(1, minmax(0, 1fr));\\n  }\\n  .flex-col {\\n    flex-direction: column;\\n  }\\n  .items-center {\\n    align-items: center;\\n  }\\n  .items-start {\\n    align-items: flex-start;\\n  }\\n  .justify-between {\\n    justify-content: space-between;\\n  }\\n  .justify-center {\\n    justify-content: center;\\n  }\\n  .gap-2 {\\n    gap: calc(var(--spacing) * 2);\\n  }\\n  .gap-4 {\\n    gap: calc(var(--spacing) * 4);\\n  }\\n  .gap-6 {\\n    gap: calc(var(--spacing) * 6);\\n  }\\n  .gap-8 {\\n    gap: calc(var(--spacing) * 8);\\n  }\\n  .space-y-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .space-y-4 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .rounded {\\n    border-radius: 0.25rem;\\n  }\\n  .rounded-full {\\n    border-radius: calc(infinity * 1px);\\n  }\\n  .rounded-lg {\\n    border-radius: var(--radius-lg);\\n  }\\n  .border {\\n    border-style: var(--tw-border-style);\\n    border-width: 1px;\\n  }\\n  .border-2 {\\n    border-style: var(--tw-border-style);\\n    border-width: 2px;\\n  }\\n  .border-gray-300 {\\n    border-color: var(--color-gray-300);\\n  }\\n  .border-white {\\n    border-color: var(--color-white);\\n  }\\n  .bg-blue-50 {\\n    background-color: var(--color-blue-50);\\n  }\\n  .bg-blue-100 {\\n    background-color: var(--color-blue-100);\\n  }\\n  .bg-blue-600 {\\n    background-color: var(--color-blue-600);\\n  }\\n  .bg-gray-50 {\\n    background-color: var(--color-gray-50);\\n  }\\n  .bg-gray-100 {\\n    background-color: var(--color-gray-100);\\n  }\\n  .bg-gray-200 {\\n    background-color: var(--color-gray-200);\\n  }\\n  .bg-green-50 {\\n    background-color: var(--color-green-50);\\n  }\\n  .bg-green-100 {\\n    background-color: var(--color-green-100);\\n  }\\n  .bg-green-600 {\\n    background-color: var(--color-green-600);\\n  }\\n  .bg-orange-50 {\\n    background-color: var(--color-orange-50);\\n  }\\n  .bg-purple-50 {\\n    background-color: var(--color-purple-50);\\n  }\\n  .bg-purple-100 {\\n    background-color: var(--color-purple-100);\\n  }\\n  .bg-purple-600 {\\n    background-color: var(--color-purple-600);\\n  }\\n  .bg-white {\\n    background-color: var(--color-white);\\n  }\\n  .bg-gradient-to-r {\\n    --tw-gradient-position: to right in oklab;\\n    background-image: linear-gradient(var(--tw-gradient-stops));\\n  }\\n  .from-blue-600 {\\n    --tw-gradient-from: var(--color-blue-600);\\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\\n  }\\n  .to-purple-600 {\\n    --tw-gradient-to: var(--color-purple-600);\\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\\n  }\\n  .p-2 {\\n    padding: calc(var(--spacing) * 2);\\n  }\\n  .p-6 {\\n    padding: calc(var(--spacing) * 6);\\n  }\\n  .p-8 {\\n    padding: calc(var(--spacing) * 8);\\n  }\\n  .px-3 {\\n    padding-inline: calc(var(--spacing) * 3);\\n  }\\n  .px-4 {\\n    padding-inline: calc(var(--spacing) * 4);\\n  }\\n  .px-8 {\\n    padding-inline: calc(var(--spacing) * 8);\\n  }\\n  .py-1 {\\n    padding-block: calc(var(--spacing) * 1);\\n  }\\n  .py-2 {\\n    padding-block: calc(var(--spacing) * 2);\\n  }\\n  .py-3 {\\n    padding-block: calc(var(--spacing) * 3);\\n  }\\n  .py-8 {\\n    padding-block: calc(var(--spacing) * 8);\\n  }\\n  .py-12 {\\n    padding-block: calc(var(--spacing) * 12);\\n  }\\n  .py-16 {\\n    padding-block: calc(var(--spacing) * 16);\\n  }\\n  .py-20 {\\n    padding-block: calc(var(--spacing) * 20);\\n  }\\n  .text-center {\\n    text-align: center;\\n  }\\n  .text-2xl {\\n    font-size: var(--text-2xl);\\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\\n  }\\n  .text-3xl {\\n    font-size: var(--text-3xl);\\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\\n  }\\n  .text-4xl {\\n    font-size: var(--text-4xl);\\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\\n  }\\n  .text-5xl {\\n    font-size: var(--text-5xl);\\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\\n  }\\n  .text-lg {\\n    font-size: var(--text-lg);\\n    line-height: var(--tw-leading, var(--text-lg--line-height));\\n  }\\n  .text-sm {\\n    font-size: var(--text-sm);\\n    line-height: var(--tw-leading, var(--text-sm--line-height));\\n  }\\n  .text-xl {\\n    font-size: var(--text-xl);\\n    line-height: var(--tw-leading, var(--text-xl--line-height));\\n  }\\n  .font-bold {\\n    --tw-font-weight: var(--font-weight-bold);\\n    font-weight: var(--font-weight-bold);\\n  }\\n  .font-medium {\\n    --tw-font-weight: var(--font-weight-medium);\\n    font-weight: var(--font-weight-medium);\\n  }\\n  .font-semibold {\\n    --tw-font-weight: var(--font-weight-semibold);\\n    font-weight: var(--font-weight-semibold);\\n  }\\n  .text-blue-600 {\\n    color: var(--color-blue-600);\\n  }\\n  .text-blue-800 {\\n    color: var(--color-blue-800);\\n  }\\n  .text-gray-500 {\\n    color: var(--color-gray-500);\\n  }\\n  .text-gray-600 {\\n    color: var(--color-gray-600);\\n  }\\n  .text-gray-700 {\\n    color: var(--color-gray-700);\\n  }\\n  .text-gray-800 {\\n    color: var(--color-gray-800);\\n  }\\n  .text-green-600 {\\n    color: var(--color-green-600);\\n  }\\n  .text-green-800 {\\n    color: var(--color-green-800);\\n  }\\n  .text-purple-600 {\\n    color: var(--color-purple-600);\\n  }\\n  .text-purple-800 {\\n    color: var(--color-purple-800);\\n  }\\n  .text-white {\\n    color: var(--color-white);\\n  }\\n  .shadow-md {\\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .filter {\\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\\n  }\\n  .transition-colors {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-shadow {\\n    transition-property: box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .hover\\\\:bg-gray-100 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--color-gray-100);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-gray-300 {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--color-gray-300);\\n      }\\n    }\\n  }\\n  .hover\\\\:bg-white {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: var(--color-white);\\n      }\\n    }\\n  }\\n  .hover\\\\:text-blue-600 {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--color-blue-600);\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow-lg {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .focus\\\\:ring-2 {\\n    &:focus {\\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n    }\\n  }\\n  .focus\\\\:ring-blue-500 {\\n    &:focus {\\n      --tw-ring-color: var(--color-blue-500);\\n    }\\n  }\\n  .focus\\\\:outline-none {\\n    &:focus {\\n      --tw-outline-style: none;\\n      outline-style: none;\\n    }\\n  }\\n  .md\\\\:grid-cols-2 {\\n    @media (width >= 48rem) {\\n      grid-template-columns: repeat(2, minmax(0, 1fr));\\n    }\\n  }\\n  .md\\\\:grid-cols-3 {\\n    @media (width >= 48rem) {\\n      grid-template-columns: repeat(3, minmax(0, 1fr));\\n    }\\n  }\\n  .md\\\\:flex-row {\\n    @media (width >= 48rem) {\\n      flex-direction: row;\\n    }\\n  }\\n  .lg\\\\:grid-cols-3 {\\n    @media (width >= 64rem) {\\n      grid-template-columns: repeat(3, minmax(0, 1fr));\\n    }\\n  }\\n}\\nbody {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n* {\\n  box-sizing: border-box;\\n}\\n@property --tw-space-y-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-border-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-gradient-position {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-gradient-from {\\n  syntax: \\\"<color>\\\";\\n  inherits: false;\\n  initial-value: #0000;\\n}\\n@property --tw-gradient-via {\\n  syntax: \\\"<color>\\\";\\n  inherits: false;\\n  initial-value: #0000;\\n}\\n@property --tw-gradient-to {\\n  syntax: \\\"<color>\\\";\\n  inherits: false;\\n  initial-value: #0000;\\n}\\n@property --tw-gradient-stops {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-gradient-via-stops {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-gradient-from-position {\\n  syntax: \\\"<length-percentage>\\\";\\n  inherits: false;\\n  initial-value: 0%;\\n}\\n@property --tw-gradient-via-position {\\n  syntax: \\\"<length-percentage>\\\";\\n  inherits: false;\\n  initial-value: 50%;\\n}\\n@property --tw-gradient-to-position {\\n  syntax: \\\"<length-percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-font-weight {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-inset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-ring-inset {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-offset-width {\\n  syntax: \\\"<length>\\\";\\n  inherits: false;\\n  initial-value: 0px;\\n}\\n@property --tw-ring-offset-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: #fff;\\n}\\n@property --tw-ring-offset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-blur {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-brightness {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-contrast {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-grayscale {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-hue-rotate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-invert {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-opacity {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-saturate {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-sepia {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-drop-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-drop-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-drop-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-drop-shadow-size {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@layer properties {\\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\\n    *, ::before, ::after, ::backdrop {\\n      --tw-space-y-reverse: 0;\\n      --tw-border-style: solid;\\n      --tw-gradient-position: initial;\\n      --tw-gradient-from: #0000;\\n      --tw-gradient-via: #0000;\\n      --tw-gradient-to: #0000;\\n      --tw-gradient-stops: initial;\\n      --tw-gradient-via-stops: initial;\\n      --tw-gradient-from-position: 0%;\\n      --tw-gradient-via-position: 50%;\\n      --tw-gradient-to-position: 100%;\\n      --tw-font-weight: initial;\\n      --tw-shadow: 0 0 #0000;\\n      --tw-shadow-color: initial;\\n      --tw-shadow-alpha: 100%;\\n      --tw-inset-shadow: 0 0 #0000;\\n      --tw-inset-shadow-color: initial;\\n      --tw-inset-shadow-alpha: 100%;\\n      --tw-ring-color: initial;\\n      --tw-ring-shadow: 0 0 #0000;\\n      --tw-inset-ring-color: initial;\\n      --tw-inset-ring-shadow: 0 0 #0000;\\n      --tw-ring-inset: initial;\\n      --tw-ring-offset-width: 0px;\\n      --tw-ring-offset-color: #fff;\\n      --tw-ring-offset-shadow: 0 0 #0000;\\n      --tw-blur: initial;\\n      --tw-brightness: initial;\\n      --tw-contrast: initial;\\n      --tw-grayscale: initial;\\n      --tw-hue-rotate: initial;\\n      --tw-invert: initial;\\n      --tw-opacity: initial;\\n      --tw-saturate: initial;\\n      --tw-sepia: initial;\\n      --tw-drop-shadow: initial;\\n      --tw-drop-shadow-color: initial;\\n      --tw-drop-shadow-alpha: 100%;\\n      --tw-drop-shadow-size: initial;\\n    }\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"<no source>\",\"webpack://node_modules/tailwindcss/index.css\",\"webpack://global.css\"],\"names\":[],\"mappings\":\"AAAA,kEAAA;ACs3BE,iBAAmB;AAt3BrB,yCAAyC;AAEzC;EACE;IACE;6DAEyD;IAEzD;8BAE0B;IAc1B,0CAA0C;IAgD1C,4CAA4C;IAC5C,6CAA6C;IAK7C,6CAA6C;IAE7C,6CAA6C;IAoD7C,yCAAyC;IACzC,4CAA4C;IAI5C,4CAA4C;IAC5C,4CAA4C;IAE5C,4CAA4C;IA4B5C,6CAA6C;IAC7C,8CAA8C;IAK9C,8CAA8C;IAE9C,8CAA8C;IAoD9C,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,2CAA2C;IAE3C,4CAA4C;IAC5C,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAyC5C,mBAAmB;IAEnB,kBAAkB;IAelB,sBAAsB;IAEtB,sBAAsB;IAOtB,mBAAmB;IACnB,0CAA0C;IAG1C,mBAAmB;IACnB,0CAA0C;IAC1C,kBAAkB;IAClB,yCAAyC;IACzC,kBAAkB;IAClB,sCAAsC;IACtC,oBAAoB;IACpB,2CAA2C;IAC3C,mBAAmB;IACnB,yCAAyC;IACzC,gBAAgB;IAChB,0BAA0B;IAc1B,yBAAyB;IACzB,2BAA2B;IAC3B,uBAAuB;IAoBvB,mBAAmB;IAkGnB,oCAAoC;IACpC,kEAAkE;IAClE,uCAAoD;IASpD,4CAAyD;EA5c5C;AADJ;AAmeb;EAOE;IAKE,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,eAAe;EAJM;EAiBvB;IAEE,gBAAgB;IAChB,8BAA8B;IAC9B,WAAW;IACX,2JASC;IACD,mEAGC;IACD,uEAGC;IACD,wCAAwC;EAtBpC;EA+BN;IACE,SAAS;IACT,cAAc;IACd,qBAAqB;EAHpB;EAUH;IACE,yCAAyC;IACzC,iCAAiC;EAFf;EASpB;IAME,kBAAkB;IAClB,oBAAoB;EAFnB;EASH;IACE,cAAc;IACd,gCAAgC;IAChC,wBAAwB;EAHxB;EAUF;IAEE,mBAAmB;EADd;EAWP;IAIE,gJAUC;IACD,wEAGC;IACD,4EAGC;IACD,cAAc;EApBZ;EA2BJ;IACE,cAAc;EADV;EAQN;IAEE,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,wBAAwB;EAJtB;EAOJ;IACE,eAAe;EADb;EAIJ;IACE,WAAW;EADT;EAUJ;IACE,cAAc;IACd,qBAAqB;IACrB,yBAAyB;EAHrB;EAUN;IACE,aAAa;EADC;EAQhB;IACE,wBAAwB;EADjB;EAQT;IACE,kBAAkB;EADZ;EAQR;IAGE,gBAAgB;EADb;EAUL;IAQE,cAAc;IACd,sBAAsB;EAFjB;EASP;IAEE,eAAe;IACf,YAAY;EAFR;EAYN;IAME,aAAa;IACb,8BAA8B;IAC9B,gCAAgC;IAChC,uBAAuB;IACvB,cAAc;IACd,gBAAgB;IAChB,6BAA6B;IAC7B,UAAU;EARW;EAevB;IACE,mBAAmB;EAD0B;EAQ/C;IACE,0BAA0B;EAD0B;EAQtD;IACE,sBAAsB;EADD;EAQvB;IACE,UAAU;EADE;EASd;IAEE;MACE,mBAAyD;MAAzD;QAAA,yDAAyD;MAAA;IAD7C;EADiC;EAUjD;IACE,gBAAgB;EADT;EAQT;IACE,wBAAwB;EADE;EAS5B;IACE,eAAe;IACf,mBAAmB;EAFS;EAS9B;IACE,oBAAoB;EADE;EAQxB;IACE,UAAU;EAD2B;EAIvC;IASE,gBAAgB;EADqB;EAQvC;IACE,gBAAgB;EADD;EAQjB;IAGE,kBAAkB;EADG;EAQvB;IAEE,YAAY;EADc;EAQ5B;IACE,wBAAwB;EADmB;AA3YnC;AAgZZ;EACE;IAAA,eAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,OAAmB;EAAA;EAAnB;IAAA,gDAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,yCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,8LAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,8LAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,oCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,wCAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,6HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,0LAAmB;EAAA;EAAnB;IAAA,uKAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,uCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,oCAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,4BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,wHAAmB;MAAnB,sIAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,sCAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,wBAAmB;MAAnB,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,mBAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;AADJ;ACn3BjB;EACE,SAAU;EACV;;cAEa;EACb,mCAAoC;EACpC,kCAAmC;AACpC;AAED;EACE,sBAAuB;AACxB;ADy2BC;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,iBAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,iBAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,iBAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,6BAAmB;EAAnB,eAAmB;EAAnB,iBAAmB;AAAA;AAAnB;EAAA,6BAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,6BAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,kBAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA;IAAA;MAAA,uBAAmB;MAAnB,wBAAmB;MAAnB,+BAAmB;MAAnB,yBAAmB;MAAnB,wBAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,+BAAmB;MAAnB,+BAAmB;MAAnB,+BAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,0BAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,6BAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,8BAAmB;MAAnB,iCAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,4BAAmB;MAAnB,kCAAmB;MAAnB,kBAAmB;MAAnB,wBAAmB;MAAnB,sBAAmB;MAAnB,uBAAmB;MAAnB,wBAAmB;MAAnB,oBAAmB;MAAnB,qBAAmB;MAAnB,sBAAmB;MAAnB,mBAAmB;MAAnB,yBAAmB;MAAnB,+BAAmB;MAAnB,4BAAmB;MAAnB,8BAAmB;IAAA;EAAA;AAAA\",\"sourcesContent\":[null,\"@layer theme, base, components, utilities;\\n\\n@layer theme {\\n  @theme default {\\n    --font-sans:\\n      ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-serif: ui-serif, Georgia, Cambria, \\\"Times New Roman\\\", Times, serif;\\n    --font-mono:\\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n\\n    --color-red-50: oklch(97.1% 0.013 17.38);\\n    --color-red-100: oklch(93.6% 0.032 17.717);\\n    --color-red-200: oklch(88.5% 0.062 18.334);\\n    --color-red-300: oklch(80.8% 0.114 19.571);\\n    --color-red-400: oklch(70.4% 0.191 22.216);\\n    --color-red-500: oklch(63.7% 0.237 25.331);\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-red-700: oklch(50.5% 0.213 27.518);\\n    --color-red-800: oklch(44.4% 0.177 26.899);\\n    --color-red-900: oklch(39.6% 0.141 25.723);\\n    --color-red-950: oklch(25.8% 0.092 26.042);\\n\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-orange-100: oklch(95.4% 0.038 75.164);\\n    --color-orange-200: oklch(90.1% 0.076 70.697);\\n    --color-orange-300: oklch(83.7% 0.128 66.29);\\n    --color-orange-400: oklch(75% 0.183 55.934);\\n    --color-orange-500: oklch(70.5% 0.213 47.604);\\n    --color-orange-600: oklch(64.6% 0.222 41.116);\\n    --color-orange-700: oklch(55.3% 0.195 38.402);\\n    --color-orange-800: oklch(47% 0.157 37.304);\\n    --color-orange-900: oklch(40.8% 0.123 38.172);\\n    --color-orange-950: oklch(26.6% 0.079 36.259);\\n\\n    --color-amber-50: oklch(98.7% 0.022 95.277);\\n    --color-amber-100: oklch(96.2% 0.059 95.617);\\n    --color-amber-200: oklch(92.4% 0.12 95.746);\\n    --color-amber-300: oklch(87.9% 0.169 91.605);\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-amber-500: oklch(76.9% 0.188 70.08);\\n    --color-amber-600: oklch(66.6% 0.179 58.318);\\n    --color-amber-700: oklch(55.5% 0.163 48.998);\\n    --color-amber-800: oklch(47.3% 0.137 46.201);\\n    --color-amber-900: oklch(41.4% 0.112 45.904);\\n    --color-amber-950: oklch(27.9% 0.077 45.635);\\n\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\\n\\n    --color-lime-50: oklch(98.6% 0.031 120.757);\\n    --color-lime-100: oklch(96.7% 0.067 122.328);\\n    --color-lime-200: oklch(93.8% 0.127 124.321);\\n    --color-lime-300: oklch(89.7% 0.196 126.665);\\n    --color-lime-400: oklch(84.1% 0.238 128.85);\\n    --color-lime-500: oklch(76.8% 0.233 130.85);\\n    --color-lime-600: oklch(64.8% 0.2 131.684);\\n    --color-lime-700: oklch(53.2% 0.157 131.589);\\n    --color-lime-800: oklch(45.3% 0.124 130.933);\\n    --color-lime-900: oklch(40.5% 0.101 131.063);\\n    --color-lime-950: oklch(27.4% 0.072 132.109);\\n\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-300: oklch(87.1% 0.15 154.449);\\n    --color-green-400: oklch(79.2% 0.209 151.711);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-green-950: oklch(26.6% 0.065 152.934);\\n\\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\\n    --color-emerald-100: oklch(95% 0.052 163.051);\\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\\n\\n    --color-teal-50: oklch(98.4% 0.014 180.72);\\n    --color-teal-100: oklch(95.3% 0.051 180.801);\\n    --color-teal-200: oklch(91% 0.096 180.426);\\n    --color-teal-300: oklch(85.5% 0.138 181.071);\\n    --color-teal-400: oklch(77.7% 0.152 181.912);\\n    --color-teal-500: oklch(70.4% 0.14 182.503);\\n    --color-teal-600: oklch(60% 0.118 184.704);\\n    --color-teal-700: oklch(51.1% 0.096 186.391);\\n    --color-teal-800: oklch(43.7% 0.078 188.216);\\n    --color-teal-900: oklch(38.6% 0.063 188.416);\\n    --color-teal-950: oklch(27.7% 0.046 192.524);\\n\\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\\n    --color-cyan-700: oklch(52% 0.105 223.128);\\n    --color-cyan-800: oklch(45% 0.085 224.283);\\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\\n\\n    --color-sky-50: oklch(97.7% 0.013 236.62);\\n    --color-sky-100: oklch(95.1% 0.026 236.824);\\n    --color-sky-200: oklch(90.1% 0.058 230.902);\\n    --color-sky-300: oklch(82.8% 0.111 230.318);\\n    --color-sky-400: oklch(74.6% 0.16 232.661);\\n    --color-sky-500: oklch(68.5% 0.169 237.323);\\n    --color-sky-600: oklch(58.8% 0.158 241.966);\\n    --color-sky-700: oklch(50% 0.134 242.749);\\n    --color-sky-800: oklch(44.3% 0.11 240.79);\\n    --color-sky-900: oklch(39.1% 0.09 240.876);\\n    --color-sky-950: oklch(29.3% 0.066 243.157);\\n\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-200: oklch(88.2% 0.059 254.128);\\n    --color-blue-300: oklch(80.9% 0.105 251.813);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-700: oklch(48.8% 0.243 264.376);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-blue-900: oklch(37.9% 0.146 265.522);\\n    --color-blue-950: oklch(28.2% 0.091 267.935);\\n\\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\\n    --color-indigo-100: oklch(93% 0.034 272.788);\\n    --color-indigo-200: oklch(87% 0.065 274.039);\\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\\n\\n    --color-violet-50: oklch(96.9% 0.016 293.756);\\n    --color-violet-100: oklch(94.3% 0.029 294.588);\\n    --color-violet-200: oklch(89.4% 0.057 293.283);\\n    --color-violet-300: oklch(81.1% 0.111 293.571);\\n    --color-violet-400: oklch(70.2% 0.183 293.541);\\n    --color-violet-500: oklch(60.6% 0.25 292.717);\\n    --color-violet-600: oklch(54.1% 0.281 293.009);\\n    --color-violet-700: oklch(49.1% 0.27 292.581);\\n    --color-violet-800: oklch(43.2% 0.232 292.759);\\n    --color-violet-900: oklch(38% 0.189 293.745);\\n    --color-violet-950: oklch(28.3% 0.141 291.089);\\n\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-200: oklch(90.2% 0.063 306.703);\\n    --color-purple-300: oklch(82.7% 0.119 306.383);\\n    --color-purple-400: oklch(71.4% 0.203 305.504);\\n    --color-purple-500: oklch(62.7% 0.265 303.9);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-700: oklch(49.6% 0.265 301.924);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-purple-900: oklch(38.1% 0.176 304.987);\\n    --color-purple-950: oklch(29.1% 0.149 302.717);\\n\\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\\n\\n    --color-pink-50: oklch(97.1% 0.014 343.198);\\n    --color-pink-100: oklch(94.8% 0.028 342.258);\\n    --color-pink-200: oklch(89.9% 0.061 343.231);\\n    --color-pink-300: oklch(82.3% 0.12 346.018);\\n    --color-pink-400: oklch(71.8% 0.202 349.761);\\n    --color-pink-500: oklch(65.6% 0.241 354.308);\\n    --color-pink-600: oklch(59.2% 0.249 0.584);\\n    --color-pink-700: oklch(52.5% 0.223 3.958);\\n    --color-pink-800: oklch(45.9% 0.187 3.815);\\n    --color-pink-900: oklch(40.8% 0.153 2.432);\\n    --color-pink-950: oklch(28.4% 0.109 3.907);\\n\\n    --color-rose-50: oklch(96.9% 0.015 12.422);\\n    --color-rose-100: oklch(94.1% 0.03 12.58);\\n    --color-rose-200: oklch(89.2% 0.058 10.001);\\n    --color-rose-300: oklch(81% 0.117 11.638);\\n    --color-rose-400: oklch(71.2% 0.194 13.428);\\n    --color-rose-500: oklch(64.5% 0.246 16.439);\\n    --color-rose-600: oklch(58.6% 0.253 17.585);\\n    --color-rose-700: oklch(51.4% 0.222 16.935);\\n    --color-rose-800: oklch(45.5% 0.188 13.697);\\n    --color-rose-900: oklch(41% 0.159 10.272);\\n    --color-rose-950: oklch(27.1% 0.105 12.094);\\n\\n    --color-slate-50: oklch(98.4% 0.003 247.858);\\n    --color-slate-100: oklch(96.8% 0.007 247.896);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-500: oklch(55.4% 0.046 257.417);\\n    --color-slate-600: oklch(44.6% 0.043 257.281);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-slate-900: oklch(20.8% 0.042 265.755);\\n    --color-slate-950: oklch(12.9% 0.042 264.695);\\n\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-gray-950: oklch(13% 0.028 261.692);\\n\\n    --color-zinc-50: oklch(98.5% 0 0);\\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\\n    --color-zinc-200: oklch(92% 0.004 286.32);\\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\\n    --color-zinc-700: oklch(37% 0.013 285.805);\\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\\n    --color-zinc-900: oklch(21% 0.006 285.885);\\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\\n\\n    --color-neutral-50: oklch(98.5% 0 0);\\n    --color-neutral-100: oklch(97% 0 0);\\n    --color-neutral-200: oklch(92.2% 0 0);\\n    --color-neutral-300: oklch(87% 0 0);\\n    --color-neutral-400: oklch(70.8% 0 0);\\n    --color-neutral-500: oklch(55.6% 0 0);\\n    --color-neutral-600: oklch(43.9% 0 0);\\n    --color-neutral-700: oklch(37.1% 0 0);\\n    --color-neutral-800: oklch(26.9% 0 0);\\n    --color-neutral-900: oklch(20.5% 0 0);\\n    --color-neutral-950: oklch(14.5% 0 0);\\n\\n    --color-stone-50: oklch(98.5% 0.001 106.423);\\n    --color-stone-100: oklch(97% 0.001 106.424);\\n    --color-stone-200: oklch(92.3% 0.003 48.717);\\n    --color-stone-300: oklch(86.9% 0.005 56.366);\\n    --color-stone-400: oklch(70.9% 0.01 56.259);\\n    --color-stone-500: oklch(55.3% 0.013 58.071);\\n    --color-stone-600: oklch(44.4% 0.011 73.639);\\n    --color-stone-700: oklch(37.4% 0.01 67.558);\\n    --color-stone-800: oklch(26.8% 0.007 34.298);\\n    --color-stone-900: oklch(21.6% 0.006 56.043);\\n    --color-stone-950: oklch(14.7% 0.004 49.25);\\n\\n    --color-black: #000;\\n    --color-white: #fff;\\n\\n    --spacing: 0.25rem;\\n\\n    --breakpoint-sm: 40rem;\\n    --breakpoint-md: 48rem;\\n    --breakpoint-lg: 64rem;\\n    --breakpoint-xl: 80rem;\\n    --breakpoint-2xl: 96rem;\\n\\n    --container-3xs: 16rem;\\n    --container-2xs: 18rem;\\n    --container-xs: 20rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --container-xl: 36rem;\\n    --container-2xl: 42rem;\\n    --container-3xl: 48rem;\\n    --container-4xl: 56rem;\\n    --container-5xl: 64rem;\\n    --container-6xl: 72rem;\\n    --container-7xl: 80rem;\\n\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --text-6xl: 3.75rem;\\n    --text-6xl--line-height: 1;\\n    --text-7xl: 4.5rem;\\n    --text-7xl--line-height: 1;\\n    --text-8xl: 6rem;\\n    --text-8xl--line-height: 1;\\n    --text-9xl: 8rem;\\n    --text-9xl--line-height: 1;\\n\\n    --font-weight-thin: 100;\\n    --font-weight-extralight: 200;\\n    --font-weight-light: 300;\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --font-weight-black: 900;\\n\\n    --tracking-tighter: -0.05em;\\n    --tracking-tight: -0.025em;\\n    --tracking-normal: 0em;\\n    --tracking-wide: 0.025em;\\n    --tracking-wider: 0.05em;\\n    --tracking-widest: 0.1em;\\n\\n    --leading-tight: 1.25;\\n    --leading-snug: 1.375;\\n    --leading-normal: 1.5;\\n    --leading-relaxed: 1.625;\\n    --leading-loose: 2;\\n\\n    --radius-xs: 0.125rem;\\n    --radius-sm: 0.25rem;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --radius-xl: 0.75rem;\\n    --radius-2xl: 1rem;\\n    --radius-3xl: 1.5rem;\\n    --radius-4xl: 2rem;\\n\\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-md:\\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n    --shadow-lg:\\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl:\\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n\\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\\n\\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\\n\\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\\n    --text-shadow-sm:\\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\\n      0px 2px 2px rgb(0 0 0 / 0.075);\\n    --text-shadow-md:\\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\\n      0px 2px 4px rgb(0 0 0 / 0.1);\\n    --text-shadow-lg:\\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\\n      0px 4px 8px rgb(0 0 0 / 0.1);\\n\\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    --animate-spin: spin 1s linear infinite;\\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --animate-bounce: bounce 1s infinite;\\n\\n    @keyframes spin {\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes pulse {\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n\\n    @keyframes bounce {\\n      0%,\\n      100% {\\n        transform: translateY(-25%);\\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n      }\\n\\n      50% {\\n        transform: none;\\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n      }\\n    }\\n\\n    --blur-xs: 4px;\\n    --blur-sm: 8px;\\n    --blur-md: 12px;\\n    --blur-lg: 16px;\\n    --blur-xl: 24px;\\n    --blur-2xl: 40px;\\n    --blur-3xl: 64px;\\n\\n    --perspective-dramatic: 100px;\\n    --perspective-near: 300px;\\n    --perspective-normal: 500px;\\n    --perspective-midrange: 800px;\\n    --perspective-distant: 1200px;\\n\\n    --aspect-video: 16 / 9;\\n\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: --theme(--font-sans, initial);\\n    --default-font-feature-settings: --theme(\\n      --font-sans--font-feature-settings,\\n      initial\\n    );\\n    --default-font-variation-settings: --theme(\\n      --font-sans--font-variation-settings,\\n      initial\\n    );\\n    --default-mono-font-family: --theme(--font-mono, initial);\\n    --default-mono-font-feature-settings: --theme(\\n      --font-mono--font-feature-settings,\\n      initial\\n    );\\n    --default-mono-font-variation-settings: --theme(\\n      --font-mono--font-variation-settings,\\n      initial\\n    );\\n  }\\n\\n  /* Deprecated */\\n  @theme default inline reference {\\n    --blur: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\\n    --radius: 0.25rem;\\n    --max-width-prose: 65ch;\\n  }\\n}\\n\\n@layer base {\\n  /*\\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n  2. Remove default margins and padding\\n  3. Reset all borders.\\n*/\\n\\n  *,\\n  ::after,\\n  ::before,\\n  ::backdrop,\\n  ::file-selector-button {\\n    box-sizing: border-box; /* 1 */\\n    margin: 0; /* 2 */\\n    padding: 0; /* 2 */\\n    border: 0 solid; /* 3 */\\n  }\\n\\n  /*\\n  1. Use a consistent sensible line-height in all browsers.\\n  2. Prevent adjustments of font size after orientation changes in iOS.\\n  3. Use a more readable tab size.\\n  4. Use the user's configured `sans` font-family by default.\\n  5. Use the user's configured `sans` font-feature-settings by default.\\n  6. Use the user's configured `sans` font-variation-settings by default.\\n  7. Disable tap highlights on iOS.\\n*/\\n\\n  html,\\n  :host {\\n    line-height: 1.5; /* 1 */\\n    -webkit-text-size-adjust: 100%; /* 2 */\\n    tab-size: 4; /* 3 */\\n    font-family: --theme(\\n      --default-font-family,\\n      ui-sans-serif,\\n      system-ui,\\n      sans-serif,\\n      \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\",\\n      \\\"Segoe UI Symbol\\\",\\n      \\\"Noto Color Emoji\\\"\\n    ); /* 4 */\\n    font-feature-settings: --theme(\\n      --default-font-feature-settings,\\n      normal\\n    ); /* 5 */\\n    font-variation-settings: --theme(\\n      --default-font-variation-settings,\\n      normal\\n    ); /* 6 */\\n    -webkit-tap-highlight-color: transparent; /* 7 */\\n  }\\n\\n  /*\\n  1. Add the correct height in Firefox.\\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n  3. Reset the default border style to a 1px solid border.\\n*/\\n\\n  hr {\\n    height: 0; /* 1 */\\n    color: inherit; /* 2 */\\n    border-top-width: 1px; /* 3 */\\n  }\\n\\n  /*\\n  Add the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n\\n  /*\\n  Remove the default font size and weight for headings.\\n*/\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n\\n  /*\\n  Reset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n\\n  /*\\n  Add the correct font weight in Edge and Safari.\\n*/\\n\\n  b,\\n  strong {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  1. Use the user's configured `mono` font-family by default.\\n  2. Use the user's configured `mono` font-feature-settings by default.\\n  3. Use the user's configured `mono` font-variation-settings by default.\\n  4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\n  code,\\n  kbd,\\n  samp,\\n  pre {\\n    font-family: --theme(\\n      --default-mono-font-family,\\n      ui-monospace,\\n      SFMono-Regular,\\n      Menlo,\\n      Monaco,\\n      Consolas,\\n      \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\",\\n      monospace\\n    ); /* 1 */\\n    font-feature-settings: --theme(\\n      --default-mono-font-feature-settings,\\n      normal\\n    ); /* 2 */\\n    font-variation-settings: --theme(\\n      --default-mono-font-variation-settings,\\n      normal\\n    ); /* 3 */\\n    font-size: 1em; /* 4 */\\n  }\\n\\n  /*\\n  Add the correct font size in all browsers.\\n*/\\n\\n  small {\\n    font-size: 80%;\\n  }\\n\\n  /*\\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\n  sub,\\n  sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n\\n  sub {\\n    bottom: -0.25em;\\n  }\\n\\n  sup {\\n    top: -0.5em;\\n  }\\n\\n  /*\\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n  3. Remove gaps between table borders by default.\\n*/\\n\\n  table {\\n    text-indent: 0; /* 1 */\\n    border-color: inherit; /* 2 */\\n    border-collapse: collapse; /* 3 */\\n  }\\n\\n  /*\\n  Use the modern Firefox focus style for all focusable elements.\\n*/\\n\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n\\n  /*\\n  Add the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\n  progress {\\n    vertical-align: baseline;\\n  }\\n\\n  /*\\n  Add the correct display in Chrome and Safari.\\n*/\\n\\n  summary {\\n    display: list-item;\\n  }\\n\\n  /*\\n  Make lists unstyled by default.\\n*/\\n\\n  ol,\\n  ul,\\n  menu {\\n    list-style: none;\\n  }\\n\\n  /*\\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n      This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\n  img,\\n  svg,\\n  video,\\n  canvas,\\n  audio,\\n  iframe,\\n  embed,\\n  object {\\n    display: block; /* 1 */\\n    vertical-align: middle; /* 2 */\\n  }\\n\\n  /*\\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\n  img,\\n  video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n\\n  /*\\n  1. Inherit font styles in all browsers.\\n  2. Remove border radius in all browsers.\\n  3. Remove background color in all browsers.\\n  4. Ensure consistent opacity for disabled states in all browsers.\\n*/\\n\\n  button,\\n  input,\\n  select,\\n  optgroup,\\n  textarea,\\n  ::file-selector-button {\\n    font: inherit; /* 1 */\\n    font-feature-settings: inherit; /* 1 */\\n    font-variation-settings: inherit; /* 1 */\\n    letter-spacing: inherit; /* 1 */\\n    color: inherit; /* 1 */\\n    border-radius: 0; /* 2 */\\n    background-color: transparent; /* 3 */\\n    opacity: 1; /* 4 */\\n  }\\n\\n  /*\\n  Restore default font weight.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  Restore indentation.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n\\n  /*\\n  Restore space after button.\\n*/\\n\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n\\n  /*\\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n*/\\n\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n\\n  /*\\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\\n*/\\n\\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\\n    ::placeholder {\\n      color: color-mix(in oklab, currentcolor 50%, transparent);\\n    }\\n  }\\n\\n  /*\\n  Prevent resizing textareas horizontally by default.\\n*/\\n\\n  textarea {\\n    resize: vertical;\\n  }\\n\\n  /*\\n  Remove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n\\n  /*\\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\\n*/\\n\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh; /* 1 */\\n    text-align: inherit; /* 2 */\\n  }\\n\\n  /*\\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\\n*/\\n\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n\\n  /*\\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\\n*/\\n\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n\\n  ::-webkit-datetime-edit,\\n  ::-webkit-datetime-edit-year-field,\\n  ::-webkit-datetime-edit-month-field,\\n  ::-webkit-datetime-edit-day-field,\\n  ::-webkit-datetime-edit-hour-field,\\n  ::-webkit-datetime-edit-minute-field,\\n  ::-webkit-datetime-edit-second-field,\\n  ::-webkit-datetime-edit-millisecond-field,\\n  ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n\\n  /*\\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n\\n  /*\\n  Correct the inability to style the border radius in iOS Safari.\\n*/\\n\\n  button,\\n  input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]),\\n  ::file-selector-button {\\n    appearance: button;\\n  }\\n\\n  /*\\n  Correct the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n  ::-webkit-inner-spin-button,\\n  ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n\\n  /*\\n  Make elements with the HTML hidden attribute stay hidden by default.\\n*/\\n\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n\\n@layer utilities {\\n  @tailwind utilities;\\n}\\n\",\"@import 'tailwindcss';\\n\\nbody {\\n  margin: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n* {\\n  box-sizing: border-box;\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css\n"));

/***/ })

});