"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_Button_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Button,Typography!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Box,Button,Typography!=!./node_modules/@mui/material/esm/index.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst Navigation = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        sx: {\n            position: 'fixed',\n            overflow: 'hidden',\n            width: '100%',\n            backgroundColor: 'blue',\n            zIndex: 1000,\n            top: 0,\n            padding: '1rem',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '2rem'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                variant: \"h6\",\n                sx: {\n                    color: 'white',\n                    marginRight: 'auto'\n                },\n                children: \"My App\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                passHref: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    sx: {\n                        color: 'white',\n                        backgroundColor: router.pathname === '/' ? 'rgba(255,255,255,0.2)' : 'transparent'\n                    },\n                    children: \"Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/about\",\n                passHref: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    sx: {\n                        color: 'white',\n                        backgroundColor: router.pathname === '/about' ? 'rgba(255,255,255,0.2)' : 'transparent'\n                    },\n                    children: \"About\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/contact\",\n                passHref: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Button_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    sx: {\n                        color: 'white',\n                        backgroundColor: router.pathname === '/contact' ? 'rgba(255,255,255,0.2)' : 'transparent'\n                    },\n                    children: \"Contact\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvbXBvbmVudHMvZ2xvYmFsL05hdmlnYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBd0Q7QUFDM0I7QUFDVztBQUV4QyxNQUFNSyxhQUF1Qjs7SUFDM0IsTUFBTUMsU0FBU0Ysc0RBQVNBO0lBRXhCLHFCQUNFLDhEQUFDSiwwRkFBR0E7UUFBQ08sSUFBSTtZQUNQQyxVQUFVO1lBQ1ZDLFVBQVU7WUFDVkMsT0FBTztZQUNQQyxpQkFBaUI7WUFDakJDLFFBQVE7WUFDUkMsS0FBSztZQUNMQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsWUFBWTtZQUNaQyxLQUFLO1FBQ1A7OzBCQUNFLDhEQUFDaEIsaUdBQVVBO2dCQUFDaUIsU0FBUTtnQkFBS1gsSUFBSTtvQkFBRVksT0FBTztvQkFBU0MsYUFBYTtnQkFBTzswQkFBRzs7Ozs7OzBCQUl0RSw4REFBQ2pCLGtEQUFJQTtnQkFBQ2tCLE1BQUs7Z0JBQUlDLFFBQVE7MEJBQ3JCLDRFQUFDcEIsNkZBQU1BO29CQUNMSyxJQUFJO3dCQUNGWSxPQUFPO3dCQUNQUixpQkFBaUJMLE9BQU9pQixRQUFRLEtBQUssTUFBTSwwQkFBMEI7b0JBQ3ZFOzhCQUNEOzs7Ozs7Ozs7OzswQkFLSCw4REFBQ3BCLGtEQUFJQTtnQkFBQ2tCLE1BQUs7Z0JBQVNDLFFBQVE7MEJBQzFCLDRFQUFDcEIsNkZBQU1BO29CQUNMSyxJQUFJO3dCQUNGWSxPQUFPO3dCQUNQUixpQkFBaUJMLE9BQU9pQixRQUFRLEtBQUssV0FBVywwQkFBMEI7b0JBQzVFOzhCQUNEOzs7Ozs7Ozs7OzswQkFLSCw4REFBQ3BCLGtEQUFJQTtnQkFBQ2tCLE1BQUs7Z0JBQVdDLFFBQVE7MEJBQzVCLDRFQUFDcEIsNkZBQU1BO29CQUNMSyxJQUFJO3dCQUNGWSxPQUFPO3dCQUNQUixpQkFBaUJMLE9BQU9pQixRQUFRLEtBQUssYUFBYSwwQkFBMEI7b0JBQzlFOzhCQUNEOzs7Ozs7Ozs7Ozs7Ozs7OztBQU1UO0dBdERNbEI7O1FBQ1dELGtEQUFTQTs7O0tBRHBCQztBQXdETixpRUFBZUEsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3J1aGlzYXdhbnQvRGVza3RvcC9TY2hvb2wvU29mdHdhcmUgRW5naW5lZXJpbmcvZ2xvYmFsLXByb3Rlc3QtdHJhY2tlci9Tb3R3YXJlLUVuZ2luZWVyaW5nL2Zyb250ZW5kL2NvbXBvbmVudHMvZ2xvYmFsL05hdmlnYXRpb24udHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJveCwgVHlwb2dyYXBoeSwgQnV0dG9uIH0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuXG5jb25zdCBOYXZpZ2F0aW9uOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8Qm94IHN4PXt7IFxuICAgICAgcG9zaXRpb246ICdmaXhlZCcsIFxuICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nLCBcbiAgICAgIHdpZHRoOiAnMTAwJScsIFxuICAgICAgYmFja2dyb3VuZENvbG9yOiAnYmx1ZScsXG4gICAgICB6SW5kZXg6IDEwMDAsXG4gICAgICB0b3A6IDAsXG4gICAgICBwYWRkaW5nOiAnMXJlbScsXG4gICAgICBkaXNwbGF5OiAnZmxleCcsXG4gICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICAgIGdhcDogJzJyZW0nXG4gICAgfX0+XG4gICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBzeD17eyBjb2xvcjogJ3doaXRlJywgbWFyZ2luUmlnaHQ6ICdhdXRvJyB9fT5cbiAgICAgICAgTXkgQXBwXG4gICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICBcbiAgICAgIDxMaW5rIGhyZWY9XCIvXCIgcGFzc0hyZWY+XG4gICAgICAgIDxCdXR0b24gXG4gICAgICAgICAgc3g9e3sgXG4gICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogcm91dGVyLnBhdGhuYW1lID09PSAnLycgPyAncmdiYSgyNTUsMjU1LDI1NSwwLjIpJyA6ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgSG9tZVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvTGluaz5cbiAgICAgIFxuICAgICAgPExpbmsgaHJlZj1cIi9hYm91dFwiIHBhc3NIcmVmPlxuICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgIHN4PXt7IFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHJvdXRlci5wYXRobmFtZSA9PT0gJy9hYm91dCcgPyAncmdiYSgyNTUsMjU1LDI1NSwwLjIpJyA6ICd0cmFuc3BhcmVudCdcbiAgICAgICAgICB9fVxuICAgICAgICA+XG4gICAgICAgICAgQWJvdXRcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L0xpbms+XG4gICAgICBcbiAgICAgIDxMaW5rIGhyZWY9XCIvY29udGFjdFwiIHBhc3NIcmVmPlxuICAgICAgICA8QnV0dG9uIFxuICAgICAgICAgIHN4PXt7IFxuICAgICAgICAgICAgY29sb3I6ICd3aGl0ZScsXG4gICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHJvdXRlci5wYXRobmFtZSA9PT0gJy9jb250YWN0JyA/ICdyZ2JhKDI1NSwyNTUsMjU1LDAuMiknIDogJ3RyYW5zcGFyZW50J1xuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICBDb250YWN0XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9MaW5rPlxuICAgIDwvQm94PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTmF2aWdhdGlvbjsiXSwibmFtZXMiOlsiQm94IiwiVHlwb2dyYXBoeSIsIkJ1dHRvbiIsIkxpbmsiLCJ1c2VSb3V0ZXIiLCJOYXZpZ2F0aW9uIiwicm91dGVyIiwic3giLCJwb3NpdGlvbiIsIm92ZXJmbG93Iiwid2lkdGgiLCJiYWNrZ3JvdW5kQ29sb3IiLCJ6SW5kZXgiLCJ0b3AiLCJwYWRkaW5nIiwiZGlzcGxheSIsImFsaWduSXRlbXMiLCJnYXAiLCJ2YXJpYW50IiwiY29sb3IiLCJtYXJnaW5SaWdodCIsImhyZWYiLCJwYXNzSHJlZiIsInBhdGhuYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ })

});