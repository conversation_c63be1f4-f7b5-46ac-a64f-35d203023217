"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst Navigation = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full bg-blue-600 z-50 px-4 py-4 flex items-center gap-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-white text-xl font-semibold mr-auto\",\n                children: \"Global Protest Tracker\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(router.pathname === '/' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/map\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(router.pathname === '/map' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Map\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/events\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(router.pathname === '/events' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Events\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                href: \"/submit\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(router.pathname === '/submit' ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Submit Event\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ })

});