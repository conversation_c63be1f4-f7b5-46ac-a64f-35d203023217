"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nvar _s = $RefreshSig$();\nconst Navigation = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = useState();\n    const isActive = (path)=>activeTab === path;\n    const handleNavClick = (path)=>{\n        setActiveTab(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 px-4 py-3\",\n        style: {\n            backgroundColor: '#4a7c59'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-white text-xl font-bold mr-auto\",\n                    children: \"Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/protests'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/protests') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/protests') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/protests') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Protests\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/map'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/map') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/map') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/map') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Map\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/submit'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/submit') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/submit') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/submit') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Submit\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/about'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/about') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/about') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/about') ? '#4a7c59' : 'white'\n                    },\n                    children: \"About\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"Cm1EyDBcYE9W+K/dlcvCQ31OjkQ=\");\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ })

});