"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\\n@layer properties;\\n@layer theme, base, components, utilities;\\n@layer theme {\\n  :root, :host {\\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-white: #fff;\\n    --spacing: 0.25rem;\\n    --container-4xl: 56rem;\\n    --container-6xl: 72rem;\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: var(--font-sans);\\n    --default-mono-font-family: var(--font-mono);\\n  }\\n}\\n@layer base {\\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\\n    box-sizing: border-box;\\n    margin: 0;\\n    padding: 0;\\n    border: 0 solid;\\n  }\\n  html, :host {\\n    line-height: 1.5;\\n    -webkit-text-size-adjust: 100%;\\n    tab-size: 4;\\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\");\\n    font-feature-settings: var(--default-font-feature-settings, normal);\\n    font-variation-settings: var(--default-font-variation-settings, normal);\\n    -webkit-tap-highlight-color: transparent;\\n  }\\n  hr {\\n    height: 0;\\n    color: inherit;\\n    border-top-width: 1px;\\n  }\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n  h1, h2, h3, h4, h5, h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n  b, strong {\\n    font-weight: bolder;\\n  }\\n  code, kbd, samp, pre {\\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace);\\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\\n    font-size: 1em;\\n  }\\n  small {\\n    font-size: 80%;\\n  }\\n  sub, sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n  sub {\\n    bottom: -0.25em;\\n  }\\n  sup {\\n    top: -0.5em;\\n  }\\n  table {\\n    text-indent: 0;\\n    border-color: inherit;\\n    border-collapse: collapse;\\n  }\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n  progress {\\n    vertical-align: baseline;\\n  }\\n  summary {\\n    display: list-item;\\n  }\\n  ol, ul, menu {\\n    list-style: none;\\n  }\\n  img, svg, video, canvas, audio, iframe, embed, object {\\n    display: block;\\n    vertical-align: middle;\\n  }\\n  img, video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n  button, input, select, optgroup, textarea, ::file-selector-button {\\n    font: inherit;\\n    font-feature-settings: inherit;\\n    font-variation-settings: inherit;\\n    letter-spacing: inherit;\\n    color: inherit;\\n    border-radius: 0;\\n    background-color: transparent;\\n    opacity: 1;\\n  }\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\\n    ::placeholder {\\n      color: currentcolor;\\n      @supports (color: color-mix(in lab, red, red)) {\\n        color: color-mix(in oklab, currentcolor 50%, transparent);\\n      }\\n    }\\n  }\\n  textarea {\\n    resize: vertical;\\n  }\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh;\\n    text-align: inherit;\\n  }\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n  button, input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]), ::file-selector-button {\\n    appearance: button;\\n  }\\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n@layer utilities {\\n  .absolute {\\n    position: absolute;\\n  }\\n  .fixed {\\n    position: fixed;\\n  }\\n  .relative {\\n    position: relative;\\n  }\\n  .top-0 {\\n    top: calc(var(--spacing) * 0);\\n  }\\n  .-bottom-16 {\\n    bottom: calc(var(--spacing) * -16);\\n  }\\n  .left-8 {\\n    left: calc(var(--spacing) * 8);\\n  }\\n  .z-50 {\\n    z-index: 50;\\n  }\\n  .container {\\n    width: 100%;\\n    @media (width >= 40rem) {\\n      max-width: 40rem;\\n    }\\n    @media (width >= 48rem) {\\n      max-width: 48rem;\\n    }\\n    @media (width >= 64rem) {\\n      max-width: 64rem;\\n    }\\n    @media (width >= 80rem) {\\n      max-width: 80rem;\\n    }\\n    @media (width >= 96rem) {\\n      max-width: 96rem;\\n    }\\n  }\\n  .mx-auto {\\n    margin-inline: auto;\\n  }\\n  .mt-1 {\\n    margin-top: calc(var(--spacing) * 1);\\n  }\\n  .mt-8 {\\n    margin-top: calc(var(--spacing) * 8);\\n  }\\n  .mr-auto {\\n    margin-right: auto;\\n  }\\n  .mb-2 {\\n    margin-bottom: calc(var(--spacing) * 2);\\n  }\\n  .mb-3 {\\n    margin-bottom: calc(var(--spacing) * 3);\\n  }\\n  .mb-4 {\\n    margin-bottom: calc(var(--spacing) * 4);\\n  }\\n  .mb-6 {\\n    margin-bottom: calc(var(--spacing) * 6);\\n  }\\n  .mb-8 {\\n    margin-bottom: calc(var(--spacing) * 8);\\n  }\\n  .ml-2 {\\n    margin-left: calc(var(--spacing) * 2);\\n  }\\n  .flex {\\n    display: flex;\\n  }\\n  .grid {\\n    display: grid;\\n  }\\n  .h-32 {\\n    height: calc(var(--spacing) * 32);\\n  }\\n  .h-48 {\\n    height: calc(var(--spacing) * 48);\\n  }\\n  .min-h-screen {\\n    min-height: 100vh;\\n  }\\n  .w-32 {\\n    width: calc(var(--spacing) * 32);\\n  }\\n  .w-full {\\n    width: 100%;\\n  }\\n  .max-w-4xl {\\n    max-width: var(--container-4xl);\\n  }\\n  .max-w-6xl {\\n    max-width: var(--container-6xl);\\n  }\\n  .flex-1 {\\n    flex: 1;\\n  }\\n  .grid-cols-1 {\\n    grid-template-columns: repeat(1, minmax(0, 1fr));\\n  }\\n  .grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n  .flex-wrap {\\n    flex-wrap: wrap;\\n  }\\n  .items-center {\\n    align-items: center;\\n  }\\n  .items-start {\\n    align-items: flex-start;\\n  }\\n  .justify-between {\\n    justify-content: space-between;\\n  }\\n  .justify-center {\\n    justify-content: center;\\n  }\\n  .gap-1 {\\n    gap: calc(var(--spacing) * 1);\\n  }\\n  .gap-2 {\\n    gap: calc(var(--spacing) * 2);\\n  }\\n  .gap-3 {\\n    gap: calc(var(--spacing) * 3);\\n  }\\n  .gap-4 {\\n    gap: calc(var(--spacing) * 4);\\n  }\\n  .gap-6 {\\n    gap: calc(var(--spacing) * 6);\\n  }\\n  .gap-8 {\\n    gap: calc(var(--spacing) * 8);\\n  }\\n  .space-y-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .space-y-4 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .space-y-6 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .overflow-hidden {\\n    overflow: hidden;\\n  }\\n  .rounded {\\n    border-radius: 0.25rem;\\n  }\\n  .rounded-full {\\n    border-radius: calc(infinity * 1px);\\n  }\\n  .rounded-lg {\\n    border-radius: var(--radius-lg);\\n  }\\n  .rounded-md {\\n    border-radius: var(--radius-md);\\n  }\\n  .border {\\n    border-style: var(--tw-border-style);\\n    border-width: 1px;\\n  }\\n  .border-4 {\\n    border-style: var(--tw-border-style);\\n    border-width: 4px;\\n  }\\n  .border-white {\\n    border-color: var(--color-white);\\n  }\\n  .bg-\\\\[\\\\#81a989\\\\] {\\n    background-color: #81a989;\\n  }\\n  .bg-blue-100 {\\n    background-color: var(--color-blue-100);\\n  }\\n  .bg-gray-50 {\\n    background-color: var(--color-gray-50);\\n  }\\n  .bg-gray-100 {\\n    background-color: var(--color-gray-100);\\n  }\\n  .bg-green-100 {\\n    background-color: var(--color-green-100);\\n  }\\n  .bg-white {\\n    background-color: var(--color-white);\\n  }\\n  .p-4 {\\n    padding: calc(var(--spacing) * 4);\\n  }\\n  .p-6 {\\n    padding: calc(var(--spacing) * 6);\\n  }\\n  .px-2 {\\n    padding-inline: calc(var(--spacing) * 2);\\n  }\\n  .px-3 {\\n    padding-inline: calc(var(--spacing) * 3);\\n  }\\n  .px-4 {\\n    padding-inline: calc(var(--spacing) * 4);\\n  }\\n  .px-6 {\\n    padding-inline: calc(var(--spacing) * 6);\\n  }\\n  .px-8 {\\n    padding-inline: calc(var(--spacing) * 8);\\n  }\\n  .py-1 {\\n    padding-block: calc(var(--spacing) * 1);\\n  }\\n  .py-2 {\\n    padding-block: calc(var(--spacing) * 2);\\n  }\\n  .py-3 {\\n    padding-block: calc(var(--spacing) * 3);\\n  }\\n  .py-16 {\\n    padding-block: calc(var(--spacing) * 16);\\n  }\\n  .py-20 {\\n    padding-block: calc(var(--spacing) * 20);\\n  }\\n  .pt-20 {\\n    padding-top: calc(var(--spacing) * 20);\\n  }\\n  .pb-8 {\\n    padding-bottom: calc(var(--spacing) * 8);\\n  }\\n  .text-center {\\n    text-align: center;\\n  }\\n  .text-2xl {\\n    font-size: var(--text-2xl);\\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\\n  }\\n  .text-3xl {\\n    font-size: var(--text-3xl);\\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\\n  }\\n  .text-4xl {\\n    font-size: var(--text-4xl);\\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\\n  }\\n  .text-lg {\\n    font-size: var(--text-lg);\\n    line-height: var(--tw-leading, var(--text-lg--line-height));\\n  }\\n  .text-sm {\\n    font-size: var(--text-sm);\\n    line-height: var(--tw-leading, var(--text-sm--line-height));\\n  }\\n  .text-xl {\\n    font-size: var(--text-xl);\\n    line-height: var(--tw-leading, var(--text-xl--line-height));\\n  }\\n  .text-xs {\\n    font-size: var(--text-xs);\\n    line-height: var(--tw-leading, var(--text-xs--line-height));\\n  }\\n  .font-bold {\\n    --tw-font-weight: var(--font-weight-bold);\\n    font-weight: var(--font-weight-bold);\\n  }\\n  .font-medium {\\n    --tw-font-weight: var(--font-weight-medium);\\n    font-weight: var(--font-weight-medium);\\n  }\\n  .font-semibold {\\n    --tw-font-weight: var(--font-weight-semibold);\\n    font-weight: var(--font-weight-semibold);\\n  }\\n  .text-blue-800 {\\n    color: var(--color-blue-800);\\n  }\\n  .text-gray-500 {\\n    color: var(--color-gray-500);\\n  }\\n  .text-gray-600 {\\n    color: var(--color-gray-600);\\n  }\\n  .text-gray-700 {\\n    color: var(--color-gray-700);\\n  }\\n  .text-gray-800 {\\n    color: var(--color-gray-800);\\n  }\\n  .text-gray-900 {\\n    color: var(--color-gray-900);\\n  }\\n  .text-green-800 {\\n    color: var(--color-green-800);\\n  }\\n  .text-white {\\n    color: var(--color-white);\\n  }\\n  .capitalize {\\n    text-transform: capitalize;\\n  }\\n  .shadow-md {\\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .transition-colors {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-shadow {\\n    transition-property: box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .duration-200 {\\n    --tw-duration: 200ms;\\n    transition-duration: 200ms;\\n  }\\n  .hover\\\\:bg-\\\\[\\\\#c8d5b9\\\\] {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: #c8d5b9;\\n      }\\n    }\\n  }\\n  .hover\\\\:text-gray-800 {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--color-gray-800);\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow-lg {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .md\\\\:grid-cols-2 {\\n    @media (width >= 48rem) {\\n      grid-template-columns: repeat(2, minmax(0, 1fr));\\n    }\\n  }\\n  .md\\\\:grid-cols-3 {\\n    @media (width >= 48rem) {\\n      grid-template-columns: repeat(3, minmax(0, 1fr));\\n    }\\n  }\\n  .lg\\\\:grid-cols-3 {\\n    @media (width >= 64rem) {\\n      grid-template-columns: repeat(3, minmax(0, 1fr));\\n    }\\n  }\\n}\\nbody {\\n  margin: 0;\\n  padding: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n* {\\n  box-sizing: border-box;\\n}\\n@property --tw-space-y-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-border-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-font-weight {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-inset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-ring-inset {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-offset-width {\\n  syntax: \\\"<length>\\\";\\n  inherits: false;\\n  initial-value: 0px;\\n}\\n@property --tw-ring-offset-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: #fff;\\n}\\n@property --tw-ring-offset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@layer properties {\\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\\n    *, ::before, ::after, ::backdrop {\\n      --tw-space-y-reverse: 0;\\n      --tw-border-style: solid;\\n      --tw-font-weight: initial;\\n      --tw-shadow: 0 0 #0000;\\n      --tw-shadow-color: initial;\\n      --tw-shadow-alpha: 100%;\\n      --tw-inset-shadow: 0 0 #0000;\\n      --tw-inset-shadow-color: initial;\\n      --tw-inset-shadow-alpha: 100%;\\n      --tw-ring-color: initial;\\n      --tw-ring-shadow: 0 0 #0000;\\n      --tw-inset-ring-color: initial;\\n      --tw-inset-ring-shadow: 0 0 #0000;\\n      --tw-ring-inset: initial;\\n      --tw-ring-offset-width: 0px;\\n      --tw-ring-offset-color: #fff;\\n      --tw-ring-offset-shadow: 0 0 #0000;\\n      --tw-duration: initial;\\n    }\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"<no source>\",\"webpack://node_modules/tailwindcss/index.css\",\"webpack://global.css\"],\"names\":[],\"mappings\":\"AAAA,kEAAA;ACs3BE,iBAAmB;AAt3BrB,yCAAyC;AAEzC;EACE;IACE;6DAEyD;IAEzD;8BAE0B;IA+D1B,6CAA6C;IAO7C,6CAA6C;IAqD7C,4CAA4C;IAO5C,4CAA4C;IAwF5C,2CAA2C;IAC3C,4CAA4C;IAI5C,4CAA4C;IAC5C,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0CAA0C;IAwC1C,mBAAmB;IAEnB,kBAAkB;IAiBlB,sBAAsB;IAEtB,sBAAsB;IAGtB,kBAAkB;IAClB,sCAAsC;IACtC,mBAAmB;IACnB,0CAA0C;IAG1C,mBAAmB;IACnB,0CAA0C;IAC1C,kBAAkB;IAClB,yCAAyC;IACzC,kBAAkB;IAClB,sCAAsC;IACtC,oBAAoB;IACpB,2CAA2C;IAC3C,mBAAmB;IACnB,yCAAyC;IAgBzC,yBAAyB;IACzB,2BAA2B;IAC3B,uBAAuB;IAmBvB,qBAAqB;IACrB,mBAAmB;IAkGnB,oCAAoC;IACpC,kEAAkE;IAClE,uCAAoD;IASpD,4CAAyD;EA5c5C;AADJ;AAmeb;EAOE;IAKE,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,eAAe;EAJM;EAiBvB;IAEE,gBAAgB;IAChB,8BAA8B;IAC9B,WAAW;IACX,2JASC;IACD,mEAGC;IACD,uEAGC;IACD,wCAAwC;EAtBpC;EA+BN;IACE,SAAS;IACT,cAAc;IACd,qBAAqB;EAHpB;EAUH;IACE,yCAAyC;IACzC,iCAAiC;EAFf;EASpB;IAME,kBAAkB;IAClB,oBAAoB;EAFnB;EASH;IACE,cAAc;IACd,gCAAgC;IAChC,wBAAwB;EAHxB;EAUF;IAEE,mBAAmB;EADd;EAWP;IAIE,gJAUC;IACD,wEAGC;IACD,4EAGC;IACD,cAAc;EApBZ;EA2BJ;IACE,cAAc;EADV;EAQN;IAEE,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,wBAAwB;EAJtB;EAOJ;IACE,eAAe;EADb;EAIJ;IACE,WAAW;EADT;EAUJ;IACE,cAAc;IACd,qBAAqB;IACrB,yBAAyB;EAHrB;EAUN;IACE,aAAa;EADC;EAQhB;IACE,wBAAwB;EADjB;EAQT;IACE,kBAAkB;EADZ;EAQR;IAGE,gBAAgB;EADb;EAUL;IAQE,cAAc;IACd,sBAAsB;EAFjB;EASP;IAEE,eAAe;IACf,YAAY;EAFR;EAYN;IAME,aAAa;IACb,8BAA8B;IAC9B,gCAAgC;IAChC,uBAAuB;IACvB,cAAc;IACd,gBAAgB;IAChB,6BAA6B;IAC7B,UAAU;EARW;EAevB;IACE,mBAAmB;EAD0B;EAQ/C;IACE,0BAA0B;EAD0B;EAQtD;IACE,sBAAsB;EADD;EAQvB;IACE,UAAU;EADE;EASd;IAEE;MACE,mBAAyD;MAAzD;QAAA,yDAAyD;MAAA;IAD7C;EADiC;EAUjD;IACE,gBAAgB;EADT;EAQT;IACE,wBAAwB;EADE;EAS5B;IACE,eAAe;IACf,mBAAmB;EAFS;EAS9B;IACE,oBAAoB;EADE;EAQxB;IACE,UAAU;EAD2B;EAIvC;IASE,gBAAgB;EADqB;EAQvC;IACE,gBAAgB;EADD;EAQjB;IAGE,kBAAkB;EADG;EAQvB;IAEE,YAAY;EADc;EAQ5B;IACE,wBAAwB;EADmB;AA3YnC;AAgZZ;EACE;IAAA,kBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,OAAmB;EAAA;EAAnB;IAAA,gDAAmB;EAAA;EAAnB;IAAA,gDAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,oCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,wCAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,6HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,uKAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,4BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;AADJ;ACn3BjB;EACE,SAAU;EACV,UAAW;EACX;;cAEa;EACb,mCAAoC;EACpC,kCAAmC;AACpC;AAED;EACE,sBAAuB;AACxB;ADw2BC;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,kBAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA;IAAA;MAAA,uBAAmB;MAAnB,wBAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,0BAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,6BAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,8BAAmB;MAAnB,iCAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,4BAAmB;MAAnB,kCAAmB;MAAnB,sBAAmB;IAAA;EAAA;AAAA\",\"sourcesContent\":[null,\"@layer theme, base, components, utilities;\\n\\n@layer theme {\\n  @theme default {\\n    --font-sans:\\n      ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-serif: ui-serif, Georgia, Cambria, \\\"Times New Roman\\\", Times, serif;\\n    --font-mono:\\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n\\n    --color-red-50: oklch(97.1% 0.013 17.38);\\n    --color-red-100: oklch(93.6% 0.032 17.717);\\n    --color-red-200: oklch(88.5% 0.062 18.334);\\n    --color-red-300: oklch(80.8% 0.114 19.571);\\n    --color-red-400: oklch(70.4% 0.191 22.216);\\n    --color-red-500: oklch(63.7% 0.237 25.331);\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-red-700: oklch(50.5% 0.213 27.518);\\n    --color-red-800: oklch(44.4% 0.177 26.899);\\n    --color-red-900: oklch(39.6% 0.141 25.723);\\n    --color-red-950: oklch(25.8% 0.092 26.042);\\n\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-orange-100: oklch(95.4% 0.038 75.164);\\n    --color-orange-200: oklch(90.1% 0.076 70.697);\\n    --color-orange-300: oklch(83.7% 0.128 66.29);\\n    --color-orange-400: oklch(75% 0.183 55.934);\\n    --color-orange-500: oklch(70.5% 0.213 47.604);\\n    --color-orange-600: oklch(64.6% 0.222 41.116);\\n    --color-orange-700: oklch(55.3% 0.195 38.402);\\n    --color-orange-800: oklch(47% 0.157 37.304);\\n    --color-orange-900: oklch(40.8% 0.123 38.172);\\n    --color-orange-950: oklch(26.6% 0.079 36.259);\\n\\n    --color-amber-50: oklch(98.7% 0.022 95.277);\\n    --color-amber-100: oklch(96.2% 0.059 95.617);\\n    --color-amber-200: oklch(92.4% 0.12 95.746);\\n    --color-amber-300: oklch(87.9% 0.169 91.605);\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-amber-500: oklch(76.9% 0.188 70.08);\\n    --color-amber-600: oklch(66.6% 0.179 58.318);\\n    --color-amber-700: oklch(55.5% 0.163 48.998);\\n    --color-amber-800: oklch(47.3% 0.137 46.201);\\n    --color-amber-900: oklch(41.4% 0.112 45.904);\\n    --color-amber-950: oklch(27.9% 0.077 45.635);\\n\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\\n\\n    --color-lime-50: oklch(98.6% 0.031 120.757);\\n    --color-lime-100: oklch(96.7% 0.067 122.328);\\n    --color-lime-200: oklch(93.8% 0.127 124.321);\\n    --color-lime-300: oklch(89.7% 0.196 126.665);\\n    --color-lime-400: oklch(84.1% 0.238 128.85);\\n    --color-lime-500: oklch(76.8% 0.233 130.85);\\n    --color-lime-600: oklch(64.8% 0.2 131.684);\\n    --color-lime-700: oklch(53.2% 0.157 131.589);\\n    --color-lime-800: oklch(45.3% 0.124 130.933);\\n    --color-lime-900: oklch(40.5% 0.101 131.063);\\n    --color-lime-950: oklch(27.4% 0.072 132.109);\\n\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-300: oklch(87.1% 0.15 154.449);\\n    --color-green-400: oklch(79.2% 0.209 151.711);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-green-950: oklch(26.6% 0.065 152.934);\\n\\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\\n    --color-emerald-100: oklch(95% 0.052 163.051);\\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\\n\\n    --color-teal-50: oklch(98.4% 0.014 180.72);\\n    --color-teal-100: oklch(95.3% 0.051 180.801);\\n    --color-teal-200: oklch(91% 0.096 180.426);\\n    --color-teal-300: oklch(85.5% 0.138 181.071);\\n    --color-teal-400: oklch(77.7% 0.152 181.912);\\n    --color-teal-500: oklch(70.4% 0.14 182.503);\\n    --color-teal-600: oklch(60% 0.118 184.704);\\n    --color-teal-700: oklch(51.1% 0.096 186.391);\\n    --color-teal-800: oklch(43.7% 0.078 188.216);\\n    --color-teal-900: oklch(38.6% 0.063 188.416);\\n    --color-teal-950: oklch(27.7% 0.046 192.524);\\n\\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\\n    --color-cyan-700: oklch(52% 0.105 223.128);\\n    --color-cyan-800: oklch(45% 0.085 224.283);\\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\\n\\n    --color-sky-50: oklch(97.7% 0.013 236.62);\\n    --color-sky-100: oklch(95.1% 0.026 236.824);\\n    --color-sky-200: oklch(90.1% 0.058 230.902);\\n    --color-sky-300: oklch(82.8% 0.111 230.318);\\n    --color-sky-400: oklch(74.6% 0.16 232.661);\\n    --color-sky-500: oklch(68.5% 0.169 237.323);\\n    --color-sky-600: oklch(58.8% 0.158 241.966);\\n    --color-sky-700: oklch(50% 0.134 242.749);\\n    --color-sky-800: oklch(44.3% 0.11 240.79);\\n    --color-sky-900: oklch(39.1% 0.09 240.876);\\n    --color-sky-950: oklch(29.3% 0.066 243.157);\\n\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-200: oklch(88.2% 0.059 254.128);\\n    --color-blue-300: oklch(80.9% 0.105 251.813);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-700: oklch(48.8% 0.243 264.376);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-blue-900: oklch(37.9% 0.146 265.522);\\n    --color-blue-950: oklch(28.2% 0.091 267.935);\\n\\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\\n    --color-indigo-100: oklch(93% 0.034 272.788);\\n    --color-indigo-200: oklch(87% 0.065 274.039);\\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\\n\\n    --color-violet-50: oklch(96.9% 0.016 293.756);\\n    --color-violet-100: oklch(94.3% 0.029 294.588);\\n    --color-violet-200: oklch(89.4% 0.057 293.283);\\n    --color-violet-300: oklch(81.1% 0.111 293.571);\\n    --color-violet-400: oklch(70.2% 0.183 293.541);\\n    --color-violet-500: oklch(60.6% 0.25 292.717);\\n    --color-violet-600: oklch(54.1% 0.281 293.009);\\n    --color-violet-700: oklch(49.1% 0.27 292.581);\\n    --color-violet-800: oklch(43.2% 0.232 292.759);\\n    --color-violet-900: oklch(38% 0.189 293.745);\\n    --color-violet-950: oklch(28.3% 0.141 291.089);\\n\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-200: oklch(90.2% 0.063 306.703);\\n    --color-purple-300: oklch(82.7% 0.119 306.383);\\n    --color-purple-400: oklch(71.4% 0.203 305.504);\\n    --color-purple-500: oklch(62.7% 0.265 303.9);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-700: oklch(49.6% 0.265 301.924);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-purple-900: oklch(38.1% 0.176 304.987);\\n    --color-purple-950: oklch(29.1% 0.149 302.717);\\n\\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\\n\\n    --color-pink-50: oklch(97.1% 0.014 343.198);\\n    --color-pink-100: oklch(94.8% 0.028 342.258);\\n    --color-pink-200: oklch(89.9% 0.061 343.231);\\n    --color-pink-300: oklch(82.3% 0.12 346.018);\\n    --color-pink-400: oklch(71.8% 0.202 349.761);\\n    --color-pink-500: oklch(65.6% 0.241 354.308);\\n    --color-pink-600: oklch(59.2% 0.249 0.584);\\n    --color-pink-700: oklch(52.5% 0.223 3.958);\\n    --color-pink-800: oklch(45.9% 0.187 3.815);\\n    --color-pink-900: oklch(40.8% 0.153 2.432);\\n    --color-pink-950: oklch(28.4% 0.109 3.907);\\n\\n    --color-rose-50: oklch(96.9% 0.015 12.422);\\n    --color-rose-100: oklch(94.1% 0.03 12.58);\\n    --color-rose-200: oklch(89.2% 0.058 10.001);\\n    --color-rose-300: oklch(81% 0.117 11.638);\\n    --color-rose-400: oklch(71.2% 0.194 13.428);\\n    --color-rose-500: oklch(64.5% 0.246 16.439);\\n    --color-rose-600: oklch(58.6% 0.253 17.585);\\n    --color-rose-700: oklch(51.4% 0.222 16.935);\\n    --color-rose-800: oklch(45.5% 0.188 13.697);\\n    --color-rose-900: oklch(41% 0.159 10.272);\\n    --color-rose-950: oklch(27.1% 0.105 12.094);\\n\\n    --color-slate-50: oklch(98.4% 0.003 247.858);\\n    --color-slate-100: oklch(96.8% 0.007 247.896);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-500: oklch(55.4% 0.046 257.417);\\n    --color-slate-600: oklch(44.6% 0.043 257.281);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-slate-900: oklch(20.8% 0.042 265.755);\\n    --color-slate-950: oklch(12.9% 0.042 264.695);\\n\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-gray-950: oklch(13% 0.028 261.692);\\n\\n    --color-zinc-50: oklch(98.5% 0 0);\\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\\n    --color-zinc-200: oklch(92% 0.004 286.32);\\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\\n    --color-zinc-700: oklch(37% 0.013 285.805);\\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\\n    --color-zinc-900: oklch(21% 0.006 285.885);\\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\\n\\n    --color-neutral-50: oklch(98.5% 0 0);\\n    --color-neutral-100: oklch(97% 0 0);\\n    --color-neutral-200: oklch(92.2% 0 0);\\n    --color-neutral-300: oklch(87% 0 0);\\n    --color-neutral-400: oklch(70.8% 0 0);\\n    --color-neutral-500: oklch(55.6% 0 0);\\n    --color-neutral-600: oklch(43.9% 0 0);\\n    --color-neutral-700: oklch(37.1% 0 0);\\n    --color-neutral-800: oklch(26.9% 0 0);\\n    --color-neutral-900: oklch(20.5% 0 0);\\n    --color-neutral-950: oklch(14.5% 0 0);\\n\\n    --color-stone-50: oklch(98.5% 0.001 106.423);\\n    --color-stone-100: oklch(97% 0.001 106.424);\\n    --color-stone-200: oklch(92.3% 0.003 48.717);\\n    --color-stone-300: oklch(86.9% 0.005 56.366);\\n    --color-stone-400: oklch(70.9% 0.01 56.259);\\n    --color-stone-500: oklch(55.3% 0.013 58.071);\\n    --color-stone-600: oklch(44.4% 0.011 73.639);\\n    --color-stone-700: oklch(37.4% 0.01 67.558);\\n    --color-stone-800: oklch(26.8% 0.007 34.298);\\n    --color-stone-900: oklch(21.6% 0.006 56.043);\\n    --color-stone-950: oklch(14.7% 0.004 49.25);\\n\\n    --color-black: #000;\\n    --color-white: #fff;\\n\\n    --spacing: 0.25rem;\\n\\n    --breakpoint-sm: 40rem;\\n    --breakpoint-md: 48rem;\\n    --breakpoint-lg: 64rem;\\n    --breakpoint-xl: 80rem;\\n    --breakpoint-2xl: 96rem;\\n\\n    --container-3xs: 16rem;\\n    --container-2xs: 18rem;\\n    --container-xs: 20rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --container-xl: 36rem;\\n    --container-2xl: 42rem;\\n    --container-3xl: 48rem;\\n    --container-4xl: 56rem;\\n    --container-5xl: 64rem;\\n    --container-6xl: 72rem;\\n    --container-7xl: 80rem;\\n\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --text-6xl: 3.75rem;\\n    --text-6xl--line-height: 1;\\n    --text-7xl: 4.5rem;\\n    --text-7xl--line-height: 1;\\n    --text-8xl: 6rem;\\n    --text-8xl--line-height: 1;\\n    --text-9xl: 8rem;\\n    --text-9xl--line-height: 1;\\n\\n    --font-weight-thin: 100;\\n    --font-weight-extralight: 200;\\n    --font-weight-light: 300;\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --font-weight-black: 900;\\n\\n    --tracking-tighter: -0.05em;\\n    --tracking-tight: -0.025em;\\n    --tracking-normal: 0em;\\n    --tracking-wide: 0.025em;\\n    --tracking-wider: 0.05em;\\n    --tracking-widest: 0.1em;\\n\\n    --leading-tight: 1.25;\\n    --leading-snug: 1.375;\\n    --leading-normal: 1.5;\\n    --leading-relaxed: 1.625;\\n    --leading-loose: 2;\\n\\n    --radius-xs: 0.125rem;\\n    --radius-sm: 0.25rem;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --radius-xl: 0.75rem;\\n    --radius-2xl: 1rem;\\n    --radius-3xl: 1.5rem;\\n    --radius-4xl: 2rem;\\n\\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-md:\\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n    --shadow-lg:\\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl:\\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n\\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\\n\\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\\n\\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\\n    --text-shadow-sm:\\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\\n      0px 2px 2px rgb(0 0 0 / 0.075);\\n    --text-shadow-md:\\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\\n      0px 2px 4px rgb(0 0 0 / 0.1);\\n    --text-shadow-lg:\\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\\n      0px 4px 8px rgb(0 0 0 / 0.1);\\n\\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    --animate-spin: spin 1s linear infinite;\\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --animate-bounce: bounce 1s infinite;\\n\\n    @keyframes spin {\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes pulse {\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n\\n    @keyframes bounce {\\n      0%,\\n      100% {\\n        transform: translateY(-25%);\\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n      }\\n\\n      50% {\\n        transform: none;\\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n      }\\n    }\\n\\n    --blur-xs: 4px;\\n    --blur-sm: 8px;\\n    --blur-md: 12px;\\n    --blur-lg: 16px;\\n    --blur-xl: 24px;\\n    --blur-2xl: 40px;\\n    --blur-3xl: 64px;\\n\\n    --perspective-dramatic: 100px;\\n    --perspective-near: 300px;\\n    --perspective-normal: 500px;\\n    --perspective-midrange: 800px;\\n    --perspective-distant: 1200px;\\n\\n    --aspect-video: 16 / 9;\\n\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: --theme(--font-sans, initial);\\n    --default-font-feature-settings: --theme(\\n      --font-sans--font-feature-settings,\\n      initial\\n    );\\n    --default-font-variation-settings: --theme(\\n      --font-sans--font-variation-settings,\\n      initial\\n    );\\n    --default-mono-font-family: --theme(--font-mono, initial);\\n    --default-mono-font-feature-settings: --theme(\\n      --font-mono--font-feature-settings,\\n      initial\\n    );\\n    --default-mono-font-variation-settings: --theme(\\n      --font-mono--font-variation-settings,\\n      initial\\n    );\\n  }\\n\\n  /* Deprecated */\\n  @theme default inline reference {\\n    --blur: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\\n    --radius: 0.25rem;\\n    --max-width-prose: 65ch;\\n  }\\n}\\n\\n@layer base {\\n  /*\\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n  2. Remove default margins and padding\\n  3. Reset all borders.\\n*/\\n\\n  *,\\n  ::after,\\n  ::before,\\n  ::backdrop,\\n  ::file-selector-button {\\n    box-sizing: border-box; /* 1 */\\n    margin: 0; /* 2 */\\n    padding: 0; /* 2 */\\n    border: 0 solid; /* 3 */\\n  }\\n\\n  /*\\n  1. Use a consistent sensible line-height in all browsers.\\n  2. Prevent adjustments of font size after orientation changes in iOS.\\n  3. Use a more readable tab size.\\n  4. Use the user's configured `sans` font-family by default.\\n  5. Use the user's configured `sans` font-feature-settings by default.\\n  6. Use the user's configured `sans` font-variation-settings by default.\\n  7. Disable tap highlights on iOS.\\n*/\\n\\n  html,\\n  :host {\\n    line-height: 1.5; /* 1 */\\n    -webkit-text-size-adjust: 100%; /* 2 */\\n    tab-size: 4; /* 3 */\\n    font-family: --theme(\\n      --default-font-family,\\n      ui-sans-serif,\\n      system-ui,\\n      sans-serif,\\n      \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\",\\n      \\\"Segoe UI Symbol\\\",\\n      \\\"Noto Color Emoji\\\"\\n    ); /* 4 */\\n    font-feature-settings: --theme(\\n      --default-font-feature-settings,\\n      normal\\n    ); /* 5 */\\n    font-variation-settings: --theme(\\n      --default-font-variation-settings,\\n      normal\\n    ); /* 6 */\\n    -webkit-tap-highlight-color: transparent; /* 7 */\\n  }\\n\\n  /*\\n  1. Add the correct height in Firefox.\\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n  3. Reset the default border style to a 1px solid border.\\n*/\\n\\n  hr {\\n    height: 0; /* 1 */\\n    color: inherit; /* 2 */\\n    border-top-width: 1px; /* 3 */\\n  }\\n\\n  /*\\n  Add the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n\\n  /*\\n  Remove the default font size and weight for headings.\\n*/\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n\\n  /*\\n  Reset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n\\n  /*\\n  Add the correct font weight in Edge and Safari.\\n*/\\n\\n  b,\\n  strong {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  1. Use the user's configured `mono` font-family by default.\\n  2. Use the user's configured `mono` font-feature-settings by default.\\n  3. Use the user's configured `mono` font-variation-settings by default.\\n  4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\n  code,\\n  kbd,\\n  samp,\\n  pre {\\n    font-family: --theme(\\n      --default-mono-font-family,\\n      ui-monospace,\\n      SFMono-Regular,\\n      Menlo,\\n      Monaco,\\n      Consolas,\\n      \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\",\\n      monospace\\n    ); /* 1 */\\n    font-feature-settings: --theme(\\n      --default-mono-font-feature-settings,\\n      normal\\n    ); /* 2 */\\n    font-variation-settings: --theme(\\n      --default-mono-font-variation-settings,\\n      normal\\n    ); /* 3 */\\n    font-size: 1em; /* 4 */\\n  }\\n\\n  /*\\n  Add the correct font size in all browsers.\\n*/\\n\\n  small {\\n    font-size: 80%;\\n  }\\n\\n  /*\\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\n  sub,\\n  sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n\\n  sub {\\n    bottom: -0.25em;\\n  }\\n\\n  sup {\\n    top: -0.5em;\\n  }\\n\\n  /*\\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n  3. Remove gaps between table borders by default.\\n*/\\n\\n  table {\\n    text-indent: 0; /* 1 */\\n    border-color: inherit; /* 2 */\\n    border-collapse: collapse; /* 3 */\\n  }\\n\\n  /*\\n  Use the modern Firefox focus style for all focusable elements.\\n*/\\n\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n\\n  /*\\n  Add the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\n  progress {\\n    vertical-align: baseline;\\n  }\\n\\n  /*\\n  Add the correct display in Chrome and Safari.\\n*/\\n\\n  summary {\\n    display: list-item;\\n  }\\n\\n  /*\\n  Make lists unstyled by default.\\n*/\\n\\n  ol,\\n  ul,\\n  menu {\\n    list-style: none;\\n  }\\n\\n  /*\\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n      This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\n  img,\\n  svg,\\n  video,\\n  canvas,\\n  audio,\\n  iframe,\\n  embed,\\n  object {\\n    display: block; /* 1 */\\n    vertical-align: middle; /* 2 */\\n  }\\n\\n  /*\\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\n  img,\\n  video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n\\n  /*\\n  1. Inherit font styles in all browsers.\\n  2. Remove border radius in all browsers.\\n  3. Remove background color in all browsers.\\n  4. Ensure consistent opacity for disabled states in all browsers.\\n*/\\n\\n  button,\\n  input,\\n  select,\\n  optgroup,\\n  textarea,\\n  ::file-selector-button {\\n    font: inherit; /* 1 */\\n    font-feature-settings: inherit; /* 1 */\\n    font-variation-settings: inherit; /* 1 */\\n    letter-spacing: inherit; /* 1 */\\n    color: inherit; /* 1 */\\n    border-radius: 0; /* 2 */\\n    background-color: transparent; /* 3 */\\n    opacity: 1; /* 4 */\\n  }\\n\\n  /*\\n  Restore default font weight.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  Restore indentation.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n\\n  /*\\n  Restore space after button.\\n*/\\n\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n\\n  /*\\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n*/\\n\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n\\n  /*\\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\\n*/\\n\\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\\n    ::placeholder {\\n      color: color-mix(in oklab, currentcolor 50%, transparent);\\n    }\\n  }\\n\\n  /*\\n  Prevent resizing textareas horizontally by default.\\n*/\\n\\n  textarea {\\n    resize: vertical;\\n  }\\n\\n  /*\\n  Remove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n\\n  /*\\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\\n*/\\n\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh; /* 1 */\\n    text-align: inherit; /* 2 */\\n  }\\n\\n  /*\\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\\n*/\\n\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n\\n  /*\\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\\n*/\\n\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n\\n  ::-webkit-datetime-edit,\\n  ::-webkit-datetime-edit-year-field,\\n  ::-webkit-datetime-edit-month-field,\\n  ::-webkit-datetime-edit-day-field,\\n  ::-webkit-datetime-edit-hour-field,\\n  ::-webkit-datetime-edit-minute-field,\\n  ::-webkit-datetime-edit-second-field,\\n  ::-webkit-datetime-edit-millisecond-field,\\n  ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n\\n  /*\\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n\\n  /*\\n  Correct the inability to style the border radius in iOS Safari.\\n*/\\n\\n  button,\\n  input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]),\\n  ::file-selector-button {\\n    appearance: button;\\n  }\\n\\n  /*\\n  Correct the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n  ::-webkit-inner-spin-button,\\n  ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n\\n  /*\\n  Make elements with the HTML hidden attribute stay hidden by default.\\n*/\\n\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n\\n@layer utilities {\\n  @tailwind utilities;\\n}\\n\",\"@import 'tailwindcss';\\n\\nbody {\\n  margin: 0;\\n  padding: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n* {\\n  box-sizing: border-box;\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css\n"));

/***/ })

});