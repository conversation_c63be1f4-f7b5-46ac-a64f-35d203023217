"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/@mui/material/esm/CssBaseline/CssBaseline.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@mui/material/esm/CssBaseline/CssBaseline.js ***!
  \*******************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   body: () => (/* binding */ body),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   html: () => (/* binding */ html),\n/* harmony export */   styles: () => (/* binding */ styles)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! prop-types */ \"(pages-dir-browser)/./node_modules/prop-types/index.js\");\n/* harmony import */ var _zero_styled_index_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../zero-styled/index.js */ \"(pages-dir-browser)/./node_modules/@mui/material/esm/zero-styled/index.js\");\n/* harmony import */ var _DefaultPropsProvider_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../DefaultPropsProvider/index.js */ \"(pages-dir-browser)/./node_modules/@mui/material/esm/DefaultPropsProvider/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ html,body,styles,default auto */ var _s = $RefreshSig$();\n\n\n\n\n// to determine if the global styles are static or dynamic\n\nconst isDynamicSupport = typeof (0,_zero_styled_index_js__WEBPACK_IMPORTED_MODULE_2__.globalCss)({}) === 'function';\nconst html = (theme, enableColorScheme)=>({\n        WebkitFontSmoothing: 'antialiased',\n        // Antialiasing.\n        MozOsxFontSmoothing: 'grayscale',\n        // Antialiasing.\n        // Change from `box-sizing: content-box` so that `width`\n        // is not affected by `padding` or `border`.\n        boxSizing: 'border-box',\n        // Fix font resize problem in iOS\n        WebkitTextSizeAdjust: '100%',\n        // When used under CssVarsProvider, colorScheme should not be applied dynamically because it will generate the stylesheet twice for server-rendered applications.\n        ...enableColorScheme && !theme.vars && {\n            colorScheme: theme.palette.mode\n        }\n    });\nconst body = (theme)=>({\n        color: (theme.vars || theme).palette.text.primary,\n        ...theme.typography.body1,\n        backgroundColor: (theme.vars || theme).palette.background.default,\n        '@media print': {\n            // Save printer ink.\n            backgroundColor: (theme.vars || theme).palette.common.white\n        }\n    });\nconst styles = function(theme) {\n    let enableColorScheme = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n    var _theme_components_MuiCssBaseline, _theme_components;\n    const colorSchemeStyles = {};\n    if (enableColorScheme && theme.colorSchemes && typeof theme.getColorSchemeSelector === 'function') {\n        Object.entries(theme.colorSchemes).forEach((param)=>{\n            let [key, scheme] = param;\n            const selector = theme.getColorSchemeSelector(key);\n            if (selector.startsWith('@')) {\n                var _scheme_palette;\n                // for @media (prefers-color-scheme), we need to target :root\n                colorSchemeStyles[selector] = {\n                    ':root': {\n                        colorScheme: (_scheme_palette = scheme.palette) === null || _scheme_palette === void 0 ? void 0 : _scheme_palette.mode\n                    }\n                };\n            } else {\n                var _scheme_palette1;\n                // else, it's likely that the selector already target an element with a class or data attribute\n                colorSchemeStyles[selector.replace(/\\s*&/, '')] = {\n                    colorScheme: (_scheme_palette1 = scheme.palette) === null || _scheme_palette1 === void 0 ? void 0 : _scheme_palette1.mode\n                };\n            }\n        });\n    }\n    let defaultStyles = {\n        html: html(theme, enableColorScheme),\n        '*, *::before, *::after': {\n            boxSizing: 'inherit'\n        },\n        'strong, b': {\n            fontWeight: theme.typography.fontWeightBold\n        },\n        body: {\n            margin: 0,\n            // Remove the margin in all browsers.\n            ...body(theme),\n            // Add support for document.body.requestFullScreen().\n            // Other elements, if background transparent, are not supported.\n            '&::backdrop': {\n                backgroundColor: (theme.vars || theme).palette.background.default\n            }\n        },\n        ...colorSchemeStyles\n    };\n    const themeOverrides = (_theme_components = theme.components) === null || _theme_components === void 0 ? void 0 : (_theme_components_MuiCssBaseline = _theme_components.MuiCssBaseline) === null || _theme_components_MuiCssBaseline === void 0 ? void 0 : _theme_components_MuiCssBaseline.styleOverrides;\n    if (themeOverrides) {\n        defaultStyles = [\n            defaultStyles,\n            themeOverrides\n        ];\n    }\n    return defaultStyles;\n};\n// `ecs` stands for enableColorScheme. This is internal logic to make it work with Pigment CSS, so shorter is better.\nconst SELECTOR = 'mui-ecs';\nconst staticStyles = (theme)=>{\n    const result = styles(theme, false);\n    const baseStyles = Array.isArray(result) ? result[0] : result;\n    if (!theme.vars && baseStyles) {\n        baseStyles.html[\":root:has(\".concat(SELECTOR, \")\")] = {\n            colorScheme: theme.palette.mode\n        };\n    }\n    if (theme.colorSchemes) {\n        Object.entries(theme.colorSchemes).forEach((param)=>{\n            let [key, scheme] = param;\n            const selector = theme.getColorSchemeSelector(key);\n            if (selector.startsWith('@')) {\n                var _scheme_palette;\n                // for @media (prefers-color-scheme), we need to target :root\n                baseStyles[selector] = {\n                    [\":root:not(:has(.\".concat(SELECTOR, \"))\")]: {\n                        colorScheme: (_scheme_palette = scheme.palette) === null || _scheme_palette === void 0 ? void 0 : _scheme_palette.mode\n                    }\n                };\n            } else {\n                var _scheme_palette1;\n                // else, it's likely that the selector already target an element with a class or data attribute\n                baseStyles[selector.replace(/\\s*&/, '')] = {\n                    [\"&:not(:has(.\".concat(SELECTOR, \"))\")]: {\n                        colorScheme: (_scheme_palette1 = scheme.palette) === null || _scheme_palette1 === void 0 ? void 0 : _scheme_palette1.mode\n                    }\n                };\n            }\n        });\n    }\n    return result;\n};\nconst GlobalStyles = (0,_zero_styled_index_js__WEBPACK_IMPORTED_MODULE_2__.globalCss)(isDynamicSupport ? (param)=>{\n    let { theme, enableColorScheme } = param;\n    return styles(theme, enableColorScheme);\n} : (param)=>{\n    let { theme } = param;\n    return staticStyles(theme);\n});\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */ function CssBaseline(inProps) {\n    _s();\n    const props = (0,_DefaultPropsProvider_index_js__WEBPACK_IMPORTED_MODULE_3__.useDefaultProps)({\n        props: inProps,\n        name: 'MuiCssBaseline'\n    });\n    const { children, enableColorScheme = false } = props;\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isDynamicSupport && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(GlobalStyles, {\n                enableColorScheme: enableColorScheme\n            }),\n            !isDynamicSupport && !enableColorScheme && /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n                className: SELECTOR,\n                style: {\n                    display: 'none'\n                }\n            }),\n            children\n        ]\n    });\n}\n_s(CssBaseline, \"HRB34DXOVy299DKIWwlPvGNNvgc=\", false, function() {\n    return [\n        _DefaultPropsProvider_index_js__WEBPACK_IMPORTED_MODULE_3__.useDefaultProps\n    ];\n});\n_c = CssBaseline;\n true ? CssBaseline.propTypes = {\n    // ┌────────────────────────────── Warning ──────────────────────────────┐\n    // │ These PropTypes are generated from the TypeScript type definitions. │\n    // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n    // └─────────────────────────────────────────────────────────────────────┘\n    /**\n   * You can wrap a node.\n   */ children: prop_types__WEBPACK_IMPORTED_MODULE_4__.node,\n    /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */ enableColorScheme: prop_types__WEBPACK_IMPORTED_MODULE_4__.bool\n} : 0;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CssBaseline);\nvar _c;\n$RefreshReg$(_c, \"CssBaseline\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@mui/material/esm/CssBaseline/CssBaseline.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/@mui/material/esm/CssBaseline/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@mui/material/esm/CssBaseline/index.js ***!
  \*************************************************************/
/***/ ((__webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _CssBaseline_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _CssBaseline_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CssBaseline.js */ \"(pages-dir-browser)/./node_modules/@mui/material/esm/CssBaseline/CssBaseline.js\");\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = __webpack_module__.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = __webpack_module__.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, __webpack_module__.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                __webpack_module__.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                __webpack_module__.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        __webpack_module__.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    __webpack_module__.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2VzbS9Dc3NCYXNlbGluZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEyQyIsInNvdXJjZXMiOlsiL1VzZXJzL3J1aGlzYXdhbnQvRGVza3RvcC9TY2hvb2wvU29mdHdhcmUgRW5naW5lZXJpbmcvZ2xvYmFsLXByb3Rlc3QtdHJhY2tlci9Tb3R3YXJlLUVuZ2luZWVyaW5nL2Zyb250ZW5kL25vZGVfbW9kdWxlcy9AbXVpL21hdGVyaWFsL2VzbS9Dc3NCYXNlbGluZS9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IH0gZnJvbSBcIi4vQ3NzQmFzZWxpbmUuanNcIjsiXSwibmFtZXMiOlsiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@mui/material/esm/CssBaseline/index.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,CssBaseline!=!@mui/material */ \"(pages-dir-browser)/__barrel_optimize__?names=Box,CssBaseline!=!./node_modules/@mui/material/esm/index.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-browser)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_global_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/global/Navigation */ \"(pages-dir-browser)/./components/global/Navigation.tsx\");\n\n\n\n\nconst App = (param)=>{\n    let { Component, pageProps } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Global Protest Tracker\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1.0, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_3__.CssBaseline, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_CssBaseline_mui_material__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                sx: {\n                    paddingTop: '100px'\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, undefined);\n};\n_c = App;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);\nvar _c;\n$RefreshReg$(_c, \"App\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQWlEO0FBRXBCO0FBQzRCO0FBRXpELE1BQU1JLE1BQU07UUFBQyxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBWTtJQUM3QyxxQkFDRSw4REFBQ04sb0ZBQUdBOzswQkFDRiw4REFBQ0Usa0RBQUlBOztrQ0FDSCw4REFBQ0s7a0NBQU07Ozs7OztrQ0FDUCw4REFBQ0M7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7Ozs7Ozs7OzswQkFFaEMsOERBQUNULDRGQUFXQTs7Ozs7MEJBQ1osOERBQUNFLHFFQUFVQTs7Ozs7MEJBQ1gsOERBQUNILG9GQUFHQTtnQkFBQ1csSUFBSTtvQkFBRUMsWUFBWTtnQkFBUTswQkFDN0IsNEVBQUNQO29CQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWhDO0tBZE1GO0FBZ0JOLGlFQUFlQSxHQUFHQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvcnVoaXNhd2FudC9EZXNrdG9wL1NjaG9vbC9Tb2Z0d2FyZSBFbmdpbmVlcmluZy9nbG9iYWwtcHJvdGVzdC10cmFja2VyL1NvdHdhcmUtRW5naW5lZXJpbmcvZnJvbnRlbmQvcGFnZXMvX2FwcC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQm94LCBDc3NCYXNlbGluZSB9IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuaW1wb3J0IHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0IE5hdmlnYXRpb24gZnJvbSAnLi4vY29tcG9uZW50cy9nbG9iYWwvTmF2aWdhdGlvbic7XG5cbmNvbnN0IEFwcCA9ICh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPEJveD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+R2xvYmFsIFByb3Rlc3QgVHJhY2tlcjwvdGl0bGU+XG4gICAgICAgIDxtZXRhIG5hbWU9XCJ2aWV3cG9ydFwiIGNvbnRlbnQ9XCJpbml0aWFsLXNjYWxlPTEuMCwgd2lkdGg9ZGV2aWNlLXdpZHRoXCIgLz5cbiAgICAgIDwvSGVhZD5cbiAgICAgIDxDc3NCYXNlbGluZSAvPlxuICAgICAgPE5hdmlnYXRpb24gLz5cbiAgICAgIDxCb3ggc3g9e3sgcGFkZGluZ1RvcDogJzEwMHB4JyB9fT5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Cb3g+XG4gICAgPC9Cb3g+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBBcHA7Il0sIm5hbWVzIjpbIkJveCIsIkNzc0Jhc2VsaW5lIiwiSGVhZCIsIk5hdmlnYXRpb24iLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJ0aXRsZSIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsInN4IiwicGFkZGluZ1RvcCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/_app.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=Box,CssBaseline!=!./node_modules/@mui/material/esm/index.js":
/*!*********************************************************************************************!*\
  !*** __barrel_optimize__?names=Box,CssBaseline!=!./node_modules/@mui/material/esm/index.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* reexport safe */ _Box_index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CssBaseline: () => (/* reexport safe */ _CssBaseline_index_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _Box_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box/index.js */ \"(pages-dir-browser)/./node_modules/@mui/material/esm/Box/index.js\");\n/* harmony import */ var _CssBaseline_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CssBaseline/index.js */ \"(pages-dir-browser)/./node_modules/@mui/material/esm/CssBaseline/index.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUJveCxDc3NCYXNlbGluZSE9IS4vbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQytDIiwic291cmNlcyI6WyIvVXNlcnMvcnVoaXNhd2FudC9EZXNrdG9wL1NjaG9vbC9Tb2Z0d2FyZSBFbmdpbmVlcmluZy9nbG9iYWwtcHJvdGVzdC10cmFja2VyL1NvdHdhcmUtRW5naW5lZXJpbmcvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL0BtdWkvbWF0ZXJpYWwvZXNtL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBCb3ggfSBmcm9tIFwiLi9Cb3gvaW5kZXguanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDc3NCYXNlbGluZSB9IGZyb20gXCIuL0Nzc0Jhc2VsaW5lL2luZGV4LmpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=Box,CssBaseline!=!./node_modules/@mui/material/esm/index.js\n"));

/***/ })

});