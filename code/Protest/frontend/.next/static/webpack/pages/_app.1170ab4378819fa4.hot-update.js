/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst Navigation = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isActive = (path)=>router.pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full bg-blue-600 z-50 px-4 py-3\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-white text-xl font-bold mr-auto\",\n                    children: \"Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/') ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/protests\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/protests') ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Protests\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/map\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/map') ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Map\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/submit\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/submit') ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"Submit\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/about\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/about') ? 'bg-white bg-opacity-20' : 'hover:bg-white hover:bg-opacity-10'),\n                    children: \"About\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"(pages-dir-browser)/./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || '';\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7bURBT2dCQTs7O2VBQUFBOzs7b0RBSjJCO0FBRTNDLE1BQU1DLFdBQVlDLE1BQWtDLElBQWU7QUFFNUQsU0FBU0YsZ0JBQ2RLLElBQVksRUFDWkMsTUFBdUIsRUFDdkJDLE9BQTJCLEVBQzNCQyxhQUF1QztJQUV2QyxJQUFJTixLQUErQixFQUFFLEVBZ0JwQyxNQUFNO1FBQ0wsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9ydWhpc2F3YW50L0Rlc2t0b3AvU2Nob29sL1NvZnR3YXJlIEVuZ2luZWVyaW5nL2dsb2JhbC1wcm90ZXN0LXRyYWNrZXIvc3JjL2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IERvbWFpbkxvY2FsZSB9IGZyb20gJy4uL3NlcnZlci9jb25maWcnXG5pbXBvcnQgdHlwZSB7IG5vcm1hbGl6ZUxvY2FsZVBhdGggYXMgTm9ybWFsaXplRm4gfSBmcm9tICcuL25vcm1hbGl6ZS1sb2NhbGUtcGF0aCdcbmltcG9ydCB0eXBlIHsgZGV0ZWN0RG9tYWluTG9jYWxlIGFzIERldGVjdEZuIH0gZnJvbSAnLi9kZXRlY3QtZG9tYWluLWxvY2FsZSdcbmltcG9ydCB7IG5vcm1hbGl6ZVBhdGhUcmFpbGluZ1NsYXNoIH0gZnJvbSAnLi9ub3JtYWxpemUtdHJhaWxpbmctc2xhc2gnXG5cbmNvbnN0IGJhc2VQYXRoID0gKHByb2Nlc3MuZW52Ll9fTkVYVF9ST1VURVJfQkFTRVBBVEggYXMgc3RyaW5nKSB8fCAnJ1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0RG9tYWluTG9jYWxlKFxuICBwYXRoOiBzdHJpbmcsXG4gIGxvY2FsZT86IHN0cmluZyB8IGZhbHNlLFxuICBsb2NhbGVzPzogcmVhZG9ubHkgc3RyaW5nW10sXG4gIGRvbWFpbkxvY2FsZXM/OiByZWFkb25seSBEb21haW5Mb2NhbGVbXVxuKSB7XG4gIGlmIChwcm9jZXNzLmVudi5fX05FWFRfSTE4Tl9TVVBQT1JUKSB7XG4gICAgY29uc3Qgbm9ybWFsaXplTG9jYWxlUGF0aDogdHlwZW9mIE5vcm1hbGl6ZUZuID1cbiAgICAgIHJlcXVpcmUoJy4vbm9ybWFsaXplLWxvY2FsZS1wYXRoJykubm9ybWFsaXplTG9jYWxlUGF0aFxuICAgIGNvbnN0IGRldGVjdERvbWFpbkxvY2FsZTogdHlwZW9mIERldGVjdEZuID1cbiAgICAgIHJlcXVpcmUoJy4vZGV0ZWN0LWRvbWFpbi1sb2NhbGUnKS5kZXRlY3REb21haW5Mb2NhbGVcblxuICAgIGNvbnN0IHRhcmdldCA9IGxvY2FsZSB8fCBub3JtYWxpemVMb2NhbGVQYXRoKHBhdGgsIGxvY2FsZXMpLmRldGVjdGVkTG9jYWxlXG4gICAgY29uc3QgZG9tYWluID0gZGV0ZWN0RG9tYWluTG9jYWxlKGRvbWFpbkxvY2FsZXMsIHVuZGVmaW5lZCwgdGFyZ2V0KVxuICAgIGlmIChkb21haW4pIHtcbiAgICAgIGNvbnN0IHByb3RvID0gYGh0dHAke2RvbWFpbi5odHRwID8gJycgOiAncyd9Oi8vYFxuICAgICAgY29uc3QgZmluYWxMb2NhbGUgPSB0YXJnZXQgPT09IGRvbWFpbi5kZWZhdWx0TG9jYWxlID8gJycgOiBgLyR7dGFyZ2V0fWBcbiAgICAgIHJldHVybiBgJHtwcm90b30ke2RvbWFpbi5kb21haW59JHtub3JtYWxpemVQYXRoVHJhaWxpbmdTbGFzaChcbiAgICAgICAgYCR7YmFzZVBhdGh9JHtmaW5hbExvY2FsZX0ke3BhdGh9YFxuICAgICAgKX1gXG4gICAgfVxuICAgIHJldHVybiBmYWxzZVxuICB9IGVsc2Uge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG59XG4iXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    default: function() {\n        return _default;\n    },\n    useLinkStatus: function() {\n        return useLinkStatus;\n    }\n});\nconst _interop_require_wildcard = __webpack_require__(/*! @swc/helpers/_/_interop_require_wildcard */ \"(pages-dir-browser)/./node_modules/@swc/helpers/esm/_interop_require_wildcard.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_wildcard._(__webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"(pages-dir-browser)/./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"(pages-dir-browser)/./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"(pages-dir-browser)/./node_modules/next/dist/client/add-base-path.js\");\nconst _usemergedref = __webpack_require__(/*! ./use-merged-ref */ \"(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js\");\nconst _erroronce = __webpack_require__(/*! ../shared/lib/utils/error-once */ \"(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options) {\n    if (false) {}\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== 'undefined' ? options.locale : 'locale' in router ? router.locale : undefined;\n        const prefetchedKey = href + '%' + as + '%' + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    router.prefetch(href, as, options).catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute('target');\n    return target && target !== '_self' || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, onNavigate) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === 'A';\n    if (isAnchorNodeName && isModifiedEvent(e) || e.currentTarget.hasAttribute('download')) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    if (!(0, _islocalurl.isLocalURL)(href)) {\n        if (replace) {\n            // browser default behavior does not replace the history state\n            // so we need to do it manually\n            e.preventDefault();\n            location.replace(href);\n        }\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        if (onNavigate) {\n            let isDefaultPrevented = false;\n            onNavigate({\n                preventDefault: ()=>{\n                    isDefaultPrevented = true;\n                }\n            });\n            if (isDefaultPrevented) {\n                return;\n            }\n        }\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if ('beforePopState' in router) {\n            router[replace ? 'replace' : 'push'](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? 'replace' : 'push'](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    navigate();\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === 'string') {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onNavigate, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === 'string' || typeof children === 'number')) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const router = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const prefetchEnabled = prefetchProp !== false;\n    if (true) {\n        function createPropError(args) {\n            return Object.defineProperty(new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                value: \"E319\",\n                enumerable: false,\n                configurable: true\n            });\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === 'href') {\n                if (props[key] == null || typeof props[key] !== 'string' && typeof props[key] !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: props[key] === null ? 'null' : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true,\n            onNavigate: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === 'as') {\n                if (props[key] && valType !== 'string' && valType !== 'object') {\n                    throw createPropError({\n                        key,\n                        expected: '`string` or `object`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'locale') {\n                if (props[key] && valType !== 'string') {\n                    throw createPropError({\n                        key,\n                        expected: '`string`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'onClick' || key === 'onMouseEnter' || key === 'onTouchStart' || key === 'onNavigate') {\n                if (props[key] && valType !== 'function') {\n                    throw createPropError({\n                        key,\n                        expected: '`function`',\n                        actual: valType\n                    });\n                }\n            } else if (key === 'replace' || key === 'scroll' || key === 'shallow' || key === 'passHref' || key === 'prefetch' || key === 'legacyBehavior') {\n                if (props[key] != null && valType !== 'boolean') {\n                    throw createPropError({\n                        key,\n                        expected: '`boolean`',\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n    }\n    const { href, as } = _react.default.useMemo({\n        \"Link.LinkComponent.useMemo\": ()=>{\n            if (!router) {\n                const resolvedHref = formatStringOrUrl(hrefProp);\n                return {\n                    href: resolvedHref,\n                    as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n                };\n            }\n            const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(router, hrefProp, true);\n            return {\n                href: resolvedHref,\n                as: asProp ? (0, _resolvehref.resolveHref)(router, asProp) : resolvedAs || resolvedHref\n            };\n        }\n    }[\"Link.LinkComponent.useMemo\"], [\n        router,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw Object.defineProperty(new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\"), \"__NEXT_ERROR_CODE\", {\n                        value: \"E320\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                throw Object.defineProperty(new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0)), \"__NEXT_ERROR_CODE\", {\n                    value: \"E266\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === 'a') {\n                throw Object.defineProperty(new Error('Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E209\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === 'object' && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: '200px'\n    });\n    const setIntersectionWithResetRef = _react.default.useCallback({\n        \"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\": (el)=>{\n            // Before the link getting observed, check if visible state need to be reset\n            if (previousAs.current !== as || previousHref.current !== href) {\n                resetVisible();\n                previousAs.current = as;\n                previousHref.current = href;\n            }\n            setIntersectionRef(el);\n        }\n    }[\"Link.LinkComponent.useCallback[setIntersectionWithResetRef]\"], [\n        as,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    const setRef = (0, _usemergedref.useMergedRef)(setIntersectionWithResetRef, childRef);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect({\n        \"Link.LinkComponent.useEffect\": ()=>{\n            // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n            if (true) {\n                return;\n            }\n            if (!router) {\n                return;\n            }\n            // If we don't need to prefetch the URL, don't do prefetch.\n            if (!isVisible || !prefetchEnabled) {\n                return;\n            }\n            // Prefetch the URL.\n            prefetch(router, href, as, {\n                locale\n            });\n        }\n    }[\"Link.LinkComponent.useEffect\"], [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        router == null ? void 0 : router.locale,\n        router\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw Object.defineProperty(new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E312\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n            }\n            if (!legacyBehavior && typeof onClick === 'function') {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === 'function') {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, onNavigate);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === 'function') {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            });\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === 'function') {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            });\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === 'a' && !('href' in child.props)) {\n        const curLocale = typeof locale !== 'undefined' ? locale : router == null ? void 0 : router.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (router == null ? void 0 : router.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, router == null ? void 0 : router.locales, router == null ? void 0 : router.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, router == null ? void 0 : router.defaultLocale));\n    }\n    if (legacyBehavior) {\n        if (true) {\n            (0, _erroronce.errorOnce)('`legacyBehavior` is deprecated and will be removed in a future ' + 'release. A codemod is available to upgrade your components:\\n\\n' + 'npx @next/codemod@latest new-link .\\n\\n' + 'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components');\n        }\n        return /*#__PURE__*/ _react.default.cloneElement(child, childProps);\n    }\n    return /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\")), \"cOEYCKQNQkvb3D7rzCm6zrDrstY=\");\n_c1 = Link;\nconst LinkStatusContext = /*#__PURE__*/ (0, _react.createContext)({\n    // We do not support link status in the Pages Router, so we always return false\n    pending: false\n});\nconst useLinkStatus = ()=>{\n    // This behaviour is like React's useFormStatus. When the component is not under\n    // a <form> tag, it will get the default value, instead of throwing an error.\n    return (0, _react.useContext)(LinkStatusContext);\n};\nconst _default = Link;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"(pages-dir-browser)/./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === 'function';\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || ''\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js":
/*!*********************************************************!*\
  !*** ./node_modules/next/dist/client/use-merged-ref.js ***!
  \*********************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useMergedRef\", ({\n    enumerable: true,\n    get: function() {\n        return useMergedRef;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\nfunction useMergedRef(refA, refB) {\n    const cleanupA = (0, _react.useRef)(null);\n    const cleanupB = (0, _react.useRef)(null);\n    // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n    // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n    // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n    // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n    // (because it hasn't been updated for React 19)\n    // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n    // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n    return (0, _react.useCallback)((current)=>{\n        if (current === null) {\n            const cleanupFnA = cleanupA.current;\n            if (cleanupFnA) {\n                cleanupA.current = null;\n                cleanupFnA();\n            }\n            const cleanupFnB = cleanupB.current;\n            if (cleanupFnB) {\n                cleanupB.current = null;\n                cleanupFnB();\n            }\n        } else {\n            if (refA) {\n                cleanupA.current = applyRef(refA, current);\n            }\n            if (refB) {\n                cleanupB.current = applyRef(refB, current);\n            }\n        }\n    }, [\n        refA,\n        refB\n    ]);\n}\nfunction applyRef(refA, current) {\n    if (typeof refA === 'function') {\n        const cleanup = refA(current);\n        if (typeof cleanup === 'function') {\n            return cleanup;\n        } else {\n            return ()=>refA(null);\n        }\n    } else {\n        refA.current = current;\n        return ()=>{\n            refA.current = null;\n        };\n    }\n}\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-merged-ref.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/client/use-merged-ref.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/utils/error-once.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"errorOnce\", ({\n    enumerable: true,\n    get: function() {\n        return errorOnce;\n    }\n}));\nlet errorOnce = (_)=>{};\nif (true) {\n    const errors = new Set();\n    errorOnce = (msg)=>{\n        if (!errors.has(msg)) {\n            console.error(msg);\n        }\n        errors.add(msg);\n    };\n} //# sourceMappingURL=error-once.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3Qvc2hhcmVkL2xpYi91dGlscy9lcnJvci1vbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7NkNBV1NBOzs7ZUFBQUE7OztBQVhULElBQUlBLFlBQVksQ0FBQ0MsS0FBZTtBQUNoQyxJQUFJQyxJQUFvQixFQUFtQjtJQUN6QyxNQUFNRyxTQUFTLElBQUlDO0lBQ25CTixZQUFZLENBQUNPO1FBQ1gsSUFBSSxDQUFDRixPQUFPRyxHQUFHLENBQUNELE1BQU07WUFDcEJFLFFBQVFDLEtBQUssQ0FBQ0g7UUFDaEI7UUFDQUYsT0FBT00sR0FBRyxDQUFDSjtJQUNiO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9ydWhpc2F3YW50L0Rlc2t0b3AvU2Nob29sL3NyYy9zaGFyZWQvbGliL3V0aWxzL2Vycm9yLW9uY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsibGV0IGVycm9yT25jZSA9IChfOiBzdHJpbmcpID0+IHt9XG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykge1xuICBjb25zdCBlcnJvcnMgPSBuZXcgU2V0PHN0cmluZz4oKVxuICBlcnJvck9uY2UgPSAobXNnOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWVycm9ycy5oYXMobXNnKSkge1xuICAgICAgY29uc29sZS5lcnJvcihtc2cpXG4gICAgfVxuICAgIGVycm9ycy5hZGQobXNnKVxuICB9XG59XG5cbmV4cG9ydCB7IGVycm9yT25jZSB9XG4iXSwibmFtZXMiOlsiZXJyb3JPbmNlIiwiXyIsInByb2Nlc3MiLCJlbnYiLCJOT0RFX0VOViIsImVycm9ycyIsIlNldCIsIm1zZyIsImhhcyIsImNvbnNvbGUiLCJlcnJvciIsImFkZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/shared/lib/utils/error-once.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"(pages-dir-browser)/./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2xpbmsuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkhBQThDIiwic291cmNlcyI6WyIvVXNlcnMvcnVoaXNhd2FudC9EZXNrdG9wL1NjaG9vbC9Tb2Z0d2FyZSBFbmdpbmVlcmluZy9nbG9iYWwtcHJvdGVzdC10cmFja2VyL1NvdHdhcmUtRW5naW5lZXJpbmcvZnJvbnRlbmQvbm9kZV9tb2R1bGVzL25leHQvbGluay5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vZGlzdC9jbGllbnQvbGluaycpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/link.js\n"));

/***/ }),

/***/ "(pages-dir-browser)/./node_modules/next/router.js":
/*!*************************************!*\
  !*** ./node_modules/next/router.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/router */ \"(pages-dir-browser)/./node_modules/next/dist/client/router.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L3JvdXRlci5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSUFBZ0QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9ydWhpc2F3YW50L0Rlc2t0b3AvU2Nob29sL1NvZnR3YXJlIEVuZ2luZWVyaW5nL2dsb2JhbC1wcm90ZXN0LXRyYWNrZXIvU290d2FyZS1FbmdpbmVlcmluZy9mcm9udGVuZC9ub2RlX21vZHVsZXMvbmV4dC9yb3V0ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L3JvdXRlcicpXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/router.js\n"));

/***/ })

});