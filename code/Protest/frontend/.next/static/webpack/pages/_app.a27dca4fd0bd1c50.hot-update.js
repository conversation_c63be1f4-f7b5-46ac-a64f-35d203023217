"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst Navigation = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isActive = (path)=>router.pathname === path;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 px-4 py-3\",\n        style: {\n            backgroundColor: '#4a7c59'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-white text-xl font-bold mr-auto\",\n                    children: \"Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/') ? '' : ''),\n                    style: {\n                        backgroundColor: isActive('/') ? '#81a989' : 'transparent'\n                    },\n                    onMouseEnter: (e)=>{\n                        if (!isActive('/')) {\n                            e.currentTarget.style.backgroundColor = '#c8d5b9';\n                            e.currentTarget.style.color = '#4a7c59';\n                        }\n                    },\n                    onMouseLeave: (e)=>{\n                        if (!isActive('/')) {\n                            e.currentTarget.style.backgroundColor = 'transparent';\n                            e.currentTarget.style.color = 'white';\n                        }\n                    },\n                    children: \"Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/protests\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/protests') ? '' : ''),\n                    style: {\n                        backgroundColor: isActive('/protests') ? '#81a989' : 'transparent'\n                    },\n                    onMouseEnter: (e)=>{\n                        if (!isActive('/protests')) {\n                            e.currentTarget.style.backgroundColor = '#c8d5b9';\n                            e.currentTarget.style.color = '#4a7c59';\n                        }\n                    },\n                    onMouseLeave: (e)=>{\n                        if (!isActive('/protests')) {\n                            e.currentTarget.style.backgroundColor = 'transparent';\n                            e.currentTarget.style.color = 'white';\n                        }\n                    },\n                    children: \"Protests\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/map\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/map') ? '' : ''),\n                    style: {\n                        backgroundColor: isActive('/map') ? '#81a989' : 'transparent'\n                    },\n                    onMouseEnter: (e)=>{\n                        if (!isActive('/map')) {\n                            e.currentTarget.style.backgroundColor = '#c8d5b9';\n                            e.currentTarget.style.color = '#4a7c59';\n                        }\n                    },\n                    onMouseLeave: (e)=>{\n                        if (!isActive('/map')) {\n                            e.currentTarget.style.backgroundColor = 'transparent';\n                            e.currentTarget.style.color = 'white';\n                        }\n                    },\n                    children: \"Map\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/submit\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/submit') ? '' : ''),\n                    style: {\n                        backgroundColor: isActive('/submit') ? '#81a989' : 'transparent'\n                    },\n                    onMouseEnter: (e)=>{\n                        if (!isActive('/submit')) {\n                            e.currentTarget.style.backgroundColor = '#c8d5b9';\n                            e.currentTarget.style.color = '#4a7c59';\n                        }\n                    },\n                    onMouseLeave: (e)=>{\n                        if (!isActive('/submit')) {\n                            e.currentTarget.style.backgroundColor = 'transparent';\n                            e.currentTarget.style.color = 'white';\n                        }\n                    },\n                    children: \"Submit\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    href: \"/about\",\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/about') ? '' : ''),\n                    style: {\n                        backgroundColor: isActive('/about') ? '#81a989' : 'transparent'\n                    },\n                    onMouseEnter: (e)=>{\n                        if (!isActive('/about')) {\n                            e.currentTarget.style.backgroundColor = '#c8d5b9';\n                            e.currentTarget.style.color = '#4a7c59';\n                        }\n                    },\n                    onMouseLeave: (e)=>{\n                        if (!isActive('/about')) {\n                            e.currentTarget.style.backgroundColor = 'transparent';\n                            e.currentTarget.style.color = 'white';\n                        }\n                    },\n                    children: \"About\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 11,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ })

});