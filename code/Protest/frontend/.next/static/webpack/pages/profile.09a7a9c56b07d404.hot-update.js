"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/profile",{

/***/ "(pages-dir-browser)/./pages/profile.tsx":
/*!***************************!*\
  !*** ./pages/profile.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst Profile = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const userData = {\n        name: 'Alex Johnson',\n        username: '@alexj_activist',\n        location: 'New York, NY',\n        joinDate: 'March 2023',\n        bio: 'Human rights advocate and community organizer. Passionate about social justice and environmental protection.',\n        stats: {\n            protestsAttended: 23,\n            postsShared: 156,\n            followersCount: 2847,\n            followingCount: 1205\n        },\n        badges: [\n            {\n                name: 'Veteran Activist',\n                icon: '🏅',\n                description: 'Attended 20+ protests'\n            },\n            {\n                name: 'Community Leader',\n                icon: '👥',\n                description: 'Organized 5+ events'\n            },\n            {\n                name: 'Climate Warrior',\n                icon: '🌱',\n                description: 'Active in environmental causes'\n            }\n        ],\n        recentActivity: [\n            {\n                id: 1,\n                type: 'attended',\n                event: 'Climate Action Rally',\n                location: 'Central Park, NY',\n                date: '2024-07-10',\n                icon: '📢'\n            },\n            {\n                id: 2,\n                type: 'shared',\n                event: 'Labor Rights Update',\n                location: 'Detroit, MI',\n                date: '2024-07-08',\n                icon: '📤'\n            },\n            {\n                id: 3,\n                type: 'organized',\n                event: 'Community Food Drive',\n                location: 'Brooklyn, NY',\n                date: '2024-07-05',\n                icon: '🎯'\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            ']\">',\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20 px-4 max-w-6xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md overflow-hidden mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-48 w-full bg-[#c8d5b9]\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-16 left-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-32 h-32 rounded-full border-4 border-white flex items-center justify-center text-4xl font-bold text-white bg-[#4a7c59]\",\n                                            children: \"AJ\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-20 pb-8 px-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-3xl font-bold text-gray-900\",\n                                                        children: userData.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 68,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: userData.username\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: [\n                                                            \"\\uD83D\\uDCCD \",\n                                                            userData.location,\n                                                            \" • Joined \",\n                                                            userData.joinDate\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 70,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-6 py-2 text-white rounded-lg bg-[#4a7c59] transition-colors hover:bg-[#81a989]\",\n                                                children: \"Edit Profile\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-6\",\n                                        children: userData.bio\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-6 mb-6\",\n                                        children: [\n                                            [\n                                                'Protests Attended',\n                                                userData.stats.protestsAttended\n                                            ],\n                                            [\n                                                'Posts Shared',\n                                                userData.stats.postsShared\n                                            ],\n                                            [\n                                                'Followers',\n                                                userData.stats.followersCount.toLocaleString()\n                                            ],\n                                            [\n                                                'Following',\n                                                userData.stats.followingCount.toLocaleString()\n                                            ]\n                                        ].map((param)=>{\n                                            let [label, value] = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold text-gray-900\",\n                                                        children: value\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 92,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-[#6b8f71]\",\n                                                        children: label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 93,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, label, true, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 17\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                                children: \"Achievements\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-4\",\n                                                children: userData.badges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xl\",\n                                                                children: badge.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                lineNumber: 104,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"font-medium text-gray-900\",\n                                                                        children: badge.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                        lineNumber: 106,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-600\",\n                                                                        children: badge.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                        lineNumber: 107,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 19\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2 mb-6\",\n                        children: [\n                            'overview',\n                            'activity',\n                            'posts',\n                            'events'\n                        ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"px-4 py-2 rounded-lg font-medium capitalize transition-colors \".concat(activeTab === tab ? 'bg-[#4a7c59] text-white' : 'bg-transparent text-[#4a7c59] border border-[#4a7c59] hover:bg-[#81a989] hover:text-white'),\n                                onClick: ()=>setActiveTab(tab),\n                                children: tab\n                            }, tab, false, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-md p-6\",\n                        children: [\n                            activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: \"Recent Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: userData.recentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-4 p-4 bg-gray-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: activity.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: [\n                                                                    activity.type === 'attended' ? 'Attended' : activity.type === 'shared' ? 'Shared' : 'Organized',\n                                                                    ' ',\n                                                                    activity.event\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                lineNumber: 143,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    activity.location,\n                                                                    \" • \",\n                                                                    activity.date\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                lineNumber: 151,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, activity.id, true, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === 'activity' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: \"All Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Comprehensive activity history will be displayed here.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === 'posts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: \"Your Posts\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Your shared posts and updates will be displayed here.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined),\n                            activeTab === 'events' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                        children: \"Events\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Events you've organized or plan to attend will be displayed here.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Profile, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = Profile;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Profile);\nvar _c;\n$RefreshReg$(_c, \"Profile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL3BhZ2VzL3Byb2ZpbGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBd0M7QUFFeEMsTUFBTUUsVUFBb0I7O0lBQ3hCLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHSCwrQ0FBUUEsQ0FBQztJQUUzQyxNQUFNSSxXQUFXO1FBQ2ZDLE1BQU07UUFDTkMsVUFBVTtRQUNWQyxVQUFVO1FBQ1ZDLFVBQVU7UUFDVkMsS0FBSztRQUNMQyxPQUFPO1lBQ0xDLGtCQUFrQjtZQUNsQkMsYUFBYTtZQUNiQyxnQkFBZ0I7WUFDaEJDLGdCQUFnQjtRQUNsQjtRQUNBQyxRQUFRO1lBQ047Z0JBQUVWLE1BQU07Z0JBQW9CVyxNQUFNO2dCQUFNQyxhQUFhO1lBQXdCO1lBQzdFO2dCQUFFWixNQUFNO2dCQUFvQlcsTUFBTTtnQkFBTUMsYUFBYTtZQUFzQjtZQUMzRTtnQkFBRVosTUFBTTtnQkFBbUJXLE1BQU07Z0JBQU1DLGFBQWE7WUFBaUM7U0FDdEY7UUFDREMsZ0JBQWdCO1lBQ2Q7Z0JBQ0VDLElBQUk7Z0JBQ0pDLE1BQU07Z0JBQ05DLE9BQU87Z0JBQ1BkLFVBQVU7Z0JBQ1ZlLE1BQU07Z0JBQ05OLE1BQU07WUFDUjtZQUNBO2dCQUNFRyxJQUFJO2dCQUNKQyxNQUFNO2dCQUNOQyxPQUFPO2dCQUNQZCxVQUFVO2dCQUNWZSxNQUFNO2dCQUNOTixNQUFNO1lBQ1I7WUFDQTtnQkFDRUcsSUFBSTtnQkFDSkMsTUFBTTtnQkFDTkMsT0FBTztnQkFDUGQsVUFBVTtnQkFDVmUsTUFBTTtnQkFDTk4sTUFBTTtZQUNSO1NBQ0Q7SUFDSDtJQUVBLHFCQUNFLDhEQUFDTztRQUFJQyxXQUFVOztZQUEyQjswQkFDeEMsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFBMkg7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQU05SSw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNEOztrRUFDQyw4REFBQ0U7d0RBQUdELFdBQVU7a0VBQW9DcEIsU0FBU0MsSUFBSTs7Ozs7O2tFQUMvRCw4REFBQ3FCO3dEQUFFRixXQUFVO2tFQUFpQnBCLFNBQVNFLFFBQVE7Ozs7OztrRUFDL0MsOERBQUNvQjt3REFBRUYsV0FBVTs7NERBQTZCOzREQUNwQ3BCLFNBQVNHLFFBQVE7NERBQUM7NERBQVdILFNBQVNJLFFBQVE7Ozs7Ozs7Ozs7Ozs7MERBR3RELDhEQUFDbUI7Z0RBQ0NILFdBQVU7MERBQ1g7Ozs7Ozs7Ozs7OztrREFLSCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXNCcEIsU0FBU0ssR0FBRzs7Ozs7O2tEQUcvQyw4REFBQ2M7d0NBQUlDLFdBQVU7a0RBQ1o7NENBQ0M7Z0RBQUM7Z0RBQXFCcEIsU0FBU00sS0FBSyxDQUFDQyxnQkFBZ0I7NkNBQUM7NENBQ3REO2dEQUFDO2dEQUFnQlAsU0FBU00sS0FBSyxDQUFDRSxXQUFXOzZDQUFDOzRDQUM1QztnREFBQztnREFBYVIsU0FBU00sS0FBSyxDQUFDRyxjQUFjLENBQUNlLGNBQWM7NkNBQUc7NENBQzdEO2dEQUFDO2dEQUFheEIsU0FBU00sS0FBSyxDQUFDSSxjQUFjLENBQUNjLGNBQWM7NkNBQUc7eUNBQzlELENBQUNDLEdBQUcsQ0FBQztnREFBQyxDQUFDQyxPQUFPQyxNQUFNO2lFQUNuQiw4REFBQ1I7Z0RBQWdCQyxXQUFVOztrRUFDekIsOERBQUNEO3dEQUFJQyxXQUFVO2tFQUFvQ087Ozs7OztrRUFDbkQsOERBQUNSO3dEQUFJQyxXQUFVO2tFQUEwQk07Ozs7Ozs7K0NBRmpDQTs7Ozs7Ozs7Ozs7a0RBUWQsOERBQUNQO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQTJDOzs7Ozs7MERBQ3pELDhEQUFDRDtnREFBSUMsV0FBVTswREFDWnBCLFNBQVNXLE1BQU0sQ0FBQ2MsR0FBRyxDQUFDLENBQUNJLE9BQU9DLHNCQUMzQiw4REFBQ1g7d0RBQWdCQyxXQUFVOzswRUFDekIsOERBQUNXO2dFQUFLWCxXQUFVOzBFQUFXUyxNQUFNakIsSUFBSTs7Ozs7OzBFQUNyQyw4REFBQ087O2tGQUNDLDhEQUFDQTt3RUFBSUMsV0FBVTtrRkFBNkJTLE1BQU01QixJQUFJOzs7Ozs7a0ZBQ3RELDhEQUFDa0I7d0VBQUlDLFdBQVU7a0ZBQXlCUyxNQUFNaEIsV0FBVzs7Ozs7Ozs7Ozs7Ozt1REFKbkRpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FjcEIsOERBQUNYO3dCQUFJQyxXQUFVO2tDQUNaOzRCQUFDOzRCQUFZOzRCQUFZOzRCQUFTO3lCQUFTLENBQUNLLEdBQUcsQ0FBQyxDQUFDTyxvQkFDaEQsOERBQUNUO2dDQUVDSCxXQUFXLGlFQUlWLE9BSEN0QixjQUFja0MsTUFDViw0QkFDQTtnQ0FFTkMsU0FBUyxJQUFNbEMsYUFBYWlDOzBDQUUzQkE7K0JBUklBOzs7Ozs7Ozs7O2tDQWNYLDhEQUFDYjt3QkFBSUMsV0FBVTs7NEJBQ1p0QixjQUFjLDRCQUNiLDhEQUFDcUI7O2tEQUNDLDhEQUFDUzt3Q0FBR1IsV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNacEIsU0FBU2MsY0FBYyxDQUFDVyxHQUFHLENBQUMsQ0FBQ1MseUJBQzVCLDhEQUFDZjtnREFBc0JDLFdBQVU7O2tFQUMvQiw4REFBQ1c7d0RBQUtYLFdBQVU7a0VBQVljLFNBQVN0QixJQUFJOzs7Ozs7a0VBQ3pDLDhEQUFDTzt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUNEO2dFQUFJQyxXQUFVOztvRUFDWmMsU0FBU2xCLElBQUksS0FBSyxhQUNmLGFBQ0FrQixTQUFTbEIsSUFBSSxLQUFLLFdBQ2xCLFdBQ0E7b0VBQWE7b0VBQ2hCa0IsU0FBU2pCLEtBQUs7Ozs7Ozs7MEVBRWpCLDhEQUFDRTtnRUFBSUMsV0FBVTs7b0VBQ1pjLFNBQVMvQixRQUFRO29FQUFDO29FQUFJK0IsU0FBU2hCLElBQUk7Ozs7Ozs7Ozs7Ozs7OytDQVpoQ2dCLFNBQVNuQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7OzRCQXFCNUJqQixjQUFjLDRCQUNiLDhEQUFDcUI7O2tEQUNDLDhEQUFDUzt3Q0FBR1IsV0FBVTtrREFBMkM7Ozs7OztrREFDekQsOERBQUNFO3dDQUFFRixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzRCQUloQ3RCLGNBQWMseUJBQ2IsOERBQUNxQjs7a0RBQ0MsOERBQUNTO3dDQUFHUixXQUFVO2tEQUEyQzs7Ozs7O2tEQUN6RCw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7NEJBSWhDdEIsY0FBYywwQkFDYiw4REFBQ3FCOztrREFDQyw4REFBQ1M7d0NBQUdSLFdBQVU7a0RBQTJDOzs7Ozs7a0RBQ3pELDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU8zQztHQXRMTXZCO0tBQUFBO0FBd0xOLGlFQUFlQSxPQUFPQSxFQUFDIiwic291cmNlcyI6WyIvVXNlcnMvcnVoaXNhd2FudC9EZXNrdG9wL1NjaG9vbC9Tb2Z0d2FyZSBFbmdpbmVlcmluZy9nbG9iYWwtcHJvdGVzdC10cmFja2VyL1NvdHdhcmUtRW5naW5lZXJpbmcvZnJvbnRlbmQvcGFnZXMvcHJvZmlsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5jb25zdCBQcm9maWxlOiBSZWFjdC5GQyA9ICgpID0+IHtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKCdvdmVydmlldycpO1xuXG4gIGNvbnN0IHVzZXJEYXRhID0ge1xuICAgIG5hbWU6ICdBbGV4IEpvaG5zb24nLFxuICAgIHVzZXJuYW1lOiAnQGFsZXhqX2FjdGl2aXN0JyxcbiAgICBsb2NhdGlvbjogJ05ldyBZb3JrLCBOWScsXG4gICAgam9pbkRhdGU6ICdNYXJjaCAyMDIzJyxcbiAgICBiaW86ICdIdW1hbiByaWdodHMgYWR2b2NhdGUgYW5kIGNvbW11bml0eSBvcmdhbml6ZXIuIFBhc3Npb25hdGUgYWJvdXQgc29jaWFsIGp1c3RpY2UgYW5kIGVudmlyb25tZW50YWwgcHJvdGVjdGlvbi4nLFxuICAgIHN0YXRzOiB7XG4gICAgICBwcm90ZXN0c0F0dGVuZGVkOiAyMyxcbiAgICAgIHBvc3RzU2hhcmVkOiAxNTYsXG4gICAgICBmb2xsb3dlcnNDb3VudDogMjg0NyxcbiAgICAgIGZvbGxvd2luZ0NvdW50OiAxMjA1XG4gICAgfSxcbiAgICBiYWRnZXM6IFtcbiAgICAgIHsgbmFtZTogJ1ZldGVyYW4gQWN0aXZpc3QnLCBpY29uOiAn8J+PhScsIGRlc2NyaXB0aW9uOiAnQXR0ZW5kZWQgMjArIHByb3Rlc3RzJyB9LFxuICAgICAgeyBuYW1lOiAnQ29tbXVuaXR5IExlYWRlcicsIGljb246ICfwn5GlJywgZGVzY3JpcHRpb246ICdPcmdhbml6ZWQgNSsgZXZlbnRzJyB9LFxuICAgICAgeyBuYW1lOiAnQ2xpbWF0ZSBXYXJyaW9yJywgaWNvbjogJ/CfjLEnLCBkZXNjcmlwdGlvbjogJ0FjdGl2ZSBpbiBlbnZpcm9ubWVudGFsIGNhdXNlcycgfVxuICAgIF0sXG4gICAgcmVjZW50QWN0aXZpdHk6IFtcbiAgICAgIHtcbiAgICAgICAgaWQ6IDEsXG4gICAgICAgIHR5cGU6ICdhdHRlbmRlZCcsXG4gICAgICAgIGV2ZW50OiAnQ2xpbWF0ZSBBY3Rpb24gUmFsbHknLFxuICAgICAgICBsb2NhdGlvbjogJ0NlbnRyYWwgUGFyaywgTlknLFxuICAgICAgICBkYXRlOiAnMjAyNC0wNy0xMCcsXG4gICAgICAgIGljb246ICfwn5OiJ1xuICAgICAgfSxcbiAgICAgIHtcbiAgICAgICAgaWQ6IDIsXG4gICAgICAgIHR5cGU6ICdzaGFyZWQnLFxuICAgICAgICBldmVudDogJ0xhYm9yIFJpZ2h0cyBVcGRhdGUnLFxuICAgICAgICBsb2NhdGlvbjogJ0RldHJvaXQsIE1JJyxcbiAgICAgICAgZGF0ZTogJzIwMjQtMDctMDgnLFxuICAgICAgICBpY29uOiAn8J+TpCdcbiAgICAgIH0sXG4gICAgICB7XG4gICAgICAgIGlkOiAzLFxuICAgICAgICB0eXBlOiAnb3JnYW5pemVkJyxcbiAgICAgICAgZXZlbnQ6ICdDb21tdW5pdHkgRm9vZCBEcml2ZScsXG4gICAgICAgIGxvY2F0aW9uOiAnQnJvb2tseW4sIE5ZJyxcbiAgICAgICAgZGF0ZTogJzIwMjQtMDctMDUnLFxuICAgICAgICBpY29uOiAn8J+OrydcbiAgICAgIH1cbiAgICBdXG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTEwMFwiPl1cIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHQtMjAgcHgtNCBtYXgtdy02eGwgbXgtYXV0b1wiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kIG92ZXJmbG93LWhpZGRlbiBtYi04XCI+XG4gICAgICAgICAgey8qIFByb2ZpbGUgSGVhZGVyICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00OCB3LWZ1bGwgYmctWyNjOGQ1YjldXCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0xNiBsZWZ0LThcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMyIGgtMzIgcm91bmRlZC1mdWxsIGJvcmRlci00IGJvcmRlci13aGl0ZSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC13aGl0ZSBiZy1bIzRhN2M1OV1cIj5cbiAgICAgICAgICAgICAgICBBSlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwdC0yMCBwYi04IHB4LThcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnQgbWItNFwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPnt1c2VyRGF0YS5uYW1lfTwvaDE+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPnt1c2VyRGF0YS51c2VybmFtZX08L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgIPCfk40ge3VzZXJEYXRhLmxvY2F0aW9ufSDigKIgSm9pbmVkIHt1c2VyRGF0YS5qb2luRGF0ZX1cbiAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNiBweS0yIHRleHQtd2hpdGUgcm91bmRlZC1sZyBiZy1bIzRhN2M1OV0gdHJhbnNpdGlvbi1jb2xvcnMgaG92ZXI6YmctWyM4MWE5ODldXCJcbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIEVkaXQgUHJvZmlsZVxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNzAwIG1iLTZcIj57dXNlckRhdGEuYmlvfTwvcD5cblxuICAgICAgICAgICAgey8qIFN0YXRzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy00IGdhcC02IG1iLTZcIj5cbiAgICAgICAgICAgICAge1tcbiAgICAgICAgICAgICAgICBbJ1Byb3Rlc3RzIEF0dGVuZGVkJywgdXNlckRhdGEuc3RhdHMucHJvdGVzdHNBdHRlbmRlZF0sXG4gICAgICAgICAgICAgICAgWydQb3N0cyBTaGFyZWQnLCB1c2VyRGF0YS5zdGF0cy5wb3N0c1NoYXJlZF0sXG4gICAgICAgICAgICAgICAgWydGb2xsb3dlcnMnLCB1c2VyRGF0YS5zdGF0cy5mb2xsb3dlcnNDb3VudC50b0xvY2FsZVN0cmluZygpXSxcbiAgICAgICAgICAgICAgICBbJ0ZvbGxvd2luZycsIHVzZXJEYXRhLnN0YXRzLmZvbGxvd2luZ0NvdW50LnRvTG9jYWxlU3RyaW5nKCldLFxuICAgICAgICAgICAgICBdLm1hcCgoW2xhYmVsLCB2YWx1ZV0pID0+IChcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17bGFiZWx9IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+e3ZhbHVlfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtWyM2YjhmNzFdXCI+e2xhYmVsfTwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogQmFkZ2VzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0zXCI+QWNoaWV2ZW1lbnRzPC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIHt1c2VyRGF0YS5iYWRnZXMubWFwKChiYWRnZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbmRleH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgYmctZ3JheS01MCBweC0zIHB5LTIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhsXCI+e2JhZGdlLmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPntiYWRnZS5uYW1lfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNjAwXCI+e2JhZGdlLmRlc2NyaXB0aW9ufTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVGFiIE5hdmlnYXRpb24gKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMiBtYi02XCI+XG4gICAgICAgICAge1snb3ZlcnZpZXcnLCAnYWN0aXZpdHknLCAncG9zdHMnLCAnZXZlbnRzJ10ubWFwKCh0YWIpID0+IChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAga2V5PXt0YWJ9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTQgcHktMiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIGNhcGl0YWxpemUgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICBhY3RpdmVUYWIgPT09IHRhYlxuICAgICAgICAgICAgICAgICAgPyAnYmctWyM0YTdjNTldIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICA6ICdiZy10cmFuc3BhcmVudCB0ZXh0LVsjNGE3YzU5XSBib3JkZXIgYm9yZGVyLVsjNGE3YzU5XSBob3ZlcjpiZy1bIzgxYTk4OV0gaG92ZXI6dGV4dC13aGl0ZSdcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEFjdGl2ZVRhYih0YWIpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7dGFifVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUYWIgQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIHNoYWRvdy1tZCBwLTZcIj5cbiAgICAgICAgICB7YWN0aXZlVGFiID09PSAnb3ZlcnZpZXcnICYmIChcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+UmVjZW50IEFjdGl2aXR5PC9oMz5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7dXNlckRhdGEucmVjZW50QWN0aXZpdHkubWFwKChhY3Rpdml0eSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e2FjdGl2aXR5LmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCBwLTQgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtMnhsXCI+e2FjdGl2aXR5Lmljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2FjdGl2aXR5LnR5cGUgPT09ICdhdHRlbmRlZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnQXR0ZW5kZWQnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDogYWN0aXZpdHkudHlwZSA9PT0gJ3NoYXJlZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAnU2hhcmVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICdPcmdhbml6ZWQnfXsnICd9XG4gICAgICAgICAgICAgICAgICAgICAgICB7YWN0aXZpdHkuZXZlbnR9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHthY3Rpdml0eS5sb2NhdGlvbn0g4oCiIHthY3Rpdml0eS5kYXRlfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7YWN0aXZlVGFiID09PSAnYWN0aXZpdHknICYmIChcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+QWxsIEFjdGl2aXR5PC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPkNvbXByZWhlbnNpdmUgYWN0aXZpdHkgaGlzdG9yeSB3aWxsIGJlIGRpc3BsYXllZCBoZXJlLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7YWN0aXZlVGFiID09PSAncG9zdHMnICYmIChcbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00XCI+WW91ciBQb3N0czwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5Zb3VyIHNoYXJlZCBwb3N0cyBhbmQgdXBkYXRlcyB3aWxsIGJlIGRpc3BsYXllZCBoZXJlLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG5cbiAgICAgICAgICB7YWN0aXZlVGFiID09PSAnZXZlbnRzJyAmJiAoXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgbWItNFwiPkV2ZW50czwvaDM+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5FdmVudHMgeW91J3ZlIG9yZ2FuaXplZCBvciBwbGFuIHRvIGF0dGVuZCB3aWxsIGJlIGRpc3BsYXllZCBoZXJlLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBQcm9maWxlO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJQcm9maWxlIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwidXNlckRhdGEiLCJuYW1lIiwidXNlcm5hbWUiLCJsb2NhdGlvbiIsImpvaW5EYXRlIiwiYmlvIiwic3RhdHMiLCJwcm90ZXN0c0F0dGVuZGVkIiwicG9zdHNTaGFyZWQiLCJmb2xsb3dlcnNDb3VudCIsImZvbGxvd2luZ0NvdW50IiwiYmFkZ2VzIiwiaWNvbiIsImRlc2NyaXB0aW9uIiwicmVjZW50QWN0aXZpdHkiLCJpZCIsInR5cGUiLCJldmVudCIsImRhdGUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJidXR0b24iLCJ0b0xvY2FsZVN0cmluZyIsIm1hcCIsImxhYmVsIiwidmFsdWUiLCJoMyIsImJhZGdlIiwiaW5kZXgiLCJzcGFuIiwidGFiIiwib25DbGljayIsImFjdGl2aXR5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/profile.tsx\n"));

/***/ })

});