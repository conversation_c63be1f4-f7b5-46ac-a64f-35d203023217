"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/feed",{

/***/ "(pages-dir-browser)/./pages/feed.tsx":
/*!************************!*\
  !*** ./pages/feed.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Feed = ()=>{\n    // Sample feed data\n    const feedItems = [\n        {\n            id: 1,\n            type: 'protest',\n            title: 'Climate Action Rally in Berlin',\n            location: 'Berlin, Germany',\n            date: '2024-07-15',\n            participants: 15000,\n            status: 'ongoing',\n            description: 'Thousands gather to demand stronger climate policies...',\n            tags: [\n                'climate',\n                'environment',\n                'policy'\n            ],\n            image: '/api/placeholder/300/200'\n        },\n        {\n            id: 2,\n            type: 'update',\n            title: 'Labor Strike Update: Day 3',\n            location: 'Detroit, MI, USA',\n            date: '2024-07-14',\n            participants: 8500,\n            status: 'ongoing',\n            description: 'Auto workers continue strike for better working conditions...',\n            tags: [\n                'labor',\n                'workers rights',\n                'union'\n            ],\n            image: '/api/placeholder/300/200'\n        },\n        {\n            id: 3,\n            type: 'protest',\n            title: 'Student Protest for Education Reform',\n            location: 'São Paulo, Brazil',\n            date: '2024-07-13',\n            participants: 12000,\n            status: 'completed',\n            description: 'University students demand affordable education...',\n            tags: [\n                'education',\n                'students',\n                'reform'\n            ],\n            image: '/api/placeholder/300/200'\n        },\n        {\n            id: 4,\n            type: 'alert',\n            title: 'Peaceful Demonstration Planned',\n            location: 'Paris, France',\n            date: '2024-07-16',\n            participants: 5000,\n            status: 'planned',\n            description: 'Citizens organizing peaceful march for social justice...',\n            tags: [\n                'social justice',\n                'peaceful',\n                'march'\n            ],\n            image: '/api/placeholder/300/200'\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ongoing':\n                return '#4a7c59';\n            case 'completed':\n                return '#81a989';\n            case 'planned':\n                return '#c8d5b9';\n            default:\n                return '#4a7c59';\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'protest':\n                return '📢';\n            case 'update':\n                return '🔄';\n            case 'alert':\n                return '⚠️';\n            default:\n                return '📍';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Navigation, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"pt-20 px-4 max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                children: \"Global Protest Feed\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Real-time updates on protests and movements worldwide\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: feedItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-2xl\",\n                                                            children: getTypeIcon(item.type)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl font-semibold text-gray-900\",\n                                                                    children: item.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 92,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-4 text-sm text-gray-500 mt-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCCD \",\n                                                                                item.location\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                            lineNumber: 94,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"\\uD83D\\uDCC5 \",\n                                                                                item.date\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                            lineNumber: 95,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                \"\\uD83D\\uDC65 \",\n                                                                                item.participants.toLocaleString(),\n                                                                                \" participants\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                            lineNumber: 96,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 93,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 rounded-full text-xs font-medium text-white capitalize\",\n                                                    style: {\n                                                        backgroundColor: getStatusColor(item.status)\n                                                    },\n                                                    children: item.status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-4\",\n                                            children: item.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-4\",\n                                            children: item.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-2 py-1 text-xs rounded-full\",\n                                                    style: {\n                                                        backgroundColor: '#c8d5b9',\n                                                        color: '#4a7c59'\n                                                    },\n                                                    children: [\n                                                        \"#\",\n                                                        tag\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center gap-1 text-gray-600 hover:text-gray-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"\\uD83D\\uDC4D\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 125,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Support\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 126,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center gap-1 text-gray-600 hover:text-gray-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"\\uD83D\\uDCAC\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Comment\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 130,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"flex items-center gap-1 text-gray-600 hover:text-gray-800\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"\\uD83D\\uDCE4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 133,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Share\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                    lineNumber: 134,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"px-4 py-2 text-sm rounded-md text-white transition-colors\",\n                                                    style: {\n                                                        backgroundColor: '#4a7c59'\n                                                    },\n                                                    onMouseEnter: (e)=>{\n                                                        e.currentTarget.style.backgroundColor = '#81a989';\n                                                    },\n                                                    onMouseLeave: (e)=>{\n                                                        e.currentTarget.style.backgroundColor = '#4a7c59';\n                                                    },\n                                                    children: \"View Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, item.id, false, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 text-white rounded-lg transition-colors\",\n                            style: {\n                                backgroundColor: '#4a7c59'\n                            },\n                            onMouseEnter: (e)=>{\n                                e.currentTarget.style.backgroundColor = '#81a989';\n                            },\n                            onMouseLeave: (e)=>{\n                                e.currentTarget.style.backgroundColor = '#4a7c59';\n                            },\n                            children: \"Load More Posts\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                lineNumber: 78,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Feed;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Feed);\nvar _c;\n$RefreshReg$(_c, \"Feed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/feed.tsx\n"));

/***/ })

});