"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css":
/*!**************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css ***!
  \**************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */\\n@layer properties;\\n@layer theme, base, components, utilities;\\n@layer theme {\\n  :root, :host {\\n    --font-sans: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-black: #000;\\n    --color-white: #fff;\\n    --spacing: 0.25rem;\\n    --container-4xl: 56rem;\\n    --container-6xl: 72rem;\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: var(--font-sans);\\n    --default-mono-font-family: var(--font-mono);\\n  }\\n}\\n@layer base {\\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\\n    box-sizing: border-box;\\n    margin: 0;\\n    padding: 0;\\n    border: 0 solid;\\n  }\\n  html, :host {\\n    line-height: 1.5;\\n    -webkit-text-size-adjust: 100%;\\n    tab-size: 4;\\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\");\\n    font-feature-settings: var(--default-font-feature-settings, normal);\\n    font-variation-settings: var(--default-font-variation-settings, normal);\\n    -webkit-tap-highlight-color: transparent;\\n  }\\n  hr {\\n    height: 0;\\n    color: inherit;\\n    border-top-width: 1px;\\n  }\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n  h1, h2, h3, h4, h5, h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n  b, strong {\\n    font-weight: bolder;\\n  }\\n  code, kbd, samp, pre {\\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace);\\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\\n    font-size: 1em;\\n  }\\n  small {\\n    font-size: 80%;\\n  }\\n  sub, sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n  sub {\\n    bottom: -0.25em;\\n  }\\n  sup {\\n    top: -0.5em;\\n  }\\n  table {\\n    text-indent: 0;\\n    border-color: inherit;\\n    border-collapse: collapse;\\n  }\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n  progress {\\n    vertical-align: baseline;\\n  }\\n  summary {\\n    display: list-item;\\n  }\\n  ol, ul, menu {\\n    list-style: none;\\n  }\\n  img, svg, video, canvas, audio, iframe, embed, object {\\n    display: block;\\n    vertical-align: middle;\\n  }\\n  img, video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n  button, input, select, optgroup, textarea, ::file-selector-button {\\n    font: inherit;\\n    font-feature-settings: inherit;\\n    font-variation-settings: inherit;\\n    letter-spacing: inherit;\\n    color: inherit;\\n    border-radius: 0;\\n    background-color: transparent;\\n    opacity: 1;\\n  }\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\\n    ::placeholder {\\n      color: currentcolor;\\n      @supports (color: color-mix(in lab, red, red)) {\\n        color: color-mix(in oklab, currentcolor 50%, transparent);\\n      }\\n    }\\n  }\\n  textarea {\\n    resize: vertical;\\n  }\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh;\\n    text-align: inherit;\\n  }\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n  button, input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]), ::file-selector-button {\\n    appearance: button;\\n  }\\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n@layer utilities {\\n  .absolute {\\n    position: absolute;\\n  }\\n  .fixed {\\n    position: fixed;\\n  }\\n  .relative {\\n    position: relative;\\n  }\\n  .top-0 {\\n    top: calc(var(--spacing) * 0);\\n  }\\n  .-bottom-16 {\\n    bottom: calc(var(--spacing) * -16);\\n  }\\n  .left-8 {\\n    left: calc(var(--spacing) * 8);\\n  }\\n  .z-50 {\\n    z-index: 50;\\n  }\\n  .container {\\n    width: 100%;\\n    @media (width >= 40rem) {\\n      max-width: 40rem;\\n    }\\n    @media (width >= 48rem) {\\n      max-width: 48rem;\\n    }\\n    @media (width >= 64rem) {\\n      max-width: 64rem;\\n    }\\n    @media (width >= 80rem) {\\n      max-width: 80rem;\\n    }\\n    @media (width >= 96rem) {\\n      max-width: 96rem;\\n    }\\n  }\\n  .mx-auto {\\n    margin-inline: auto;\\n  }\\n  .mt-1 {\\n    margin-top: calc(var(--spacing) * 1);\\n  }\\n  .mt-2 {\\n    margin-top: calc(var(--spacing) * 2);\\n  }\\n  .mt-8 {\\n    margin-top: calc(var(--spacing) * 8);\\n  }\\n  .mr-4 {\\n    margin-right: calc(var(--spacing) * 4);\\n  }\\n  .mr-auto {\\n    margin-right: auto;\\n  }\\n  .mb-2 {\\n    margin-bottom: calc(var(--spacing) * 2);\\n  }\\n  .mb-3 {\\n    margin-bottom: calc(var(--spacing) * 3);\\n  }\\n  .mb-4 {\\n    margin-bottom: calc(var(--spacing) * 4);\\n  }\\n  .mb-6 {\\n    margin-bottom: calc(var(--spacing) * 6);\\n  }\\n  .mb-8 {\\n    margin-bottom: calc(var(--spacing) * 8);\\n  }\\n  .ml-2 {\\n    margin-left: calc(var(--spacing) * 2);\\n  }\\n  .flex {\\n    display: flex;\\n  }\\n  .grid {\\n    display: grid;\\n  }\\n  .h-10 {\\n    height: calc(var(--spacing) * 10);\\n  }\\n  .h-32 {\\n    height: calc(var(--spacing) * 32);\\n  }\\n  .h-48 {\\n    height: calc(var(--spacing) * 48);\\n  }\\n  .min-h-screen {\\n    min-height: 100vh;\\n  }\\n  .w-1\\\\/3 {\\n    width: calc(1/3 * 100%);\\n  }\\n  .w-2\\\\/3 {\\n    width: calc(2/3 * 100%);\\n  }\\n  .w-10 {\\n    width: calc(var(--spacing) * 10);\\n  }\\n  .w-32 {\\n    width: calc(var(--spacing) * 32);\\n  }\\n  .w-full {\\n    width: 100%;\\n  }\\n  .max-w-4xl {\\n    max-width: var(--container-4xl);\\n  }\\n  .max-w-6xl {\\n    max-width: var(--container-6xl);\\n  }\\n  .flex-1 {\\n    flex: 1;\\n  }\\n  .grid-cols-1 {\\n    grid-template-columns: repeat(1, minmax(0, 1fr));\\n  }\\n  .grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n  .flex-wrap {\\n    flex-wrap: wrap;\\n  }\\n  .items-center {\\n    align-items: center;\\n  }\\n  .items-start {\\n    align-items: flex-start;\\n  }\\n  .justify-between {\\n    justify-content: space-between;\\n  }\\n  .justify-center {\\n    justify-content: center;\\n  }\\n  .gap-1 {\\n    gap: calc(var(--spacing) * 1);\\n  }\\n  .gap-2 {\\n    gap: calc(var(--spacing) * 2);\\n  }\\n  .gap-3 {\\n    gap: calc(var(--spacing) * 3);\\n  }\\n  .gap-4 {\\n    gap: calc(var(--spacing) * 4);\\n  }\\n  .gap-6 {\\n    gap: calc(var(--spacing) * 6);\\n  }\\n  .gap-8 {\\n    gap: calc(var(--spacing) * 8);\\n  }\\n  .space-y-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .space-y-4 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .space-y-6 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-y-reverse: 0;\\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\\n    }\\n  }\\n  .space-x-2 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-x-reverse: 0;\\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\\n    }\\n  }\\n  .space-x-4 {\\n    :where(& > :not(:last-child)) {\\n      --tw-space-x-reverse: 0;\\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\\n    }\\n  }\\n  .overflow-hidden {\\n    overflow: hidden;\\n  }\\n  .rounded {\\n    border-radius: 0.25rem;\\n  }\\n  .rounded-full {\\n    border-radius: calc(infinity * 1px);\\n  }\\n  .rounded-lg {\\n    border-radius: var(--radius-lg);\\n  }\\n  .rounded-md {\\n    border-radius: var(--radius-md);\\n  }\\n  .border {\\n    border-style: var(--tw-border-style);\\n    border-width: 1px;\\n  }\\n  .border-2 {\\n    border-style: var(--tw-border-style);\\n    border-width: 2px;\\n  }\\n  .border-4 {\\n    border-style: var(--tw-border-style);\\n    border-width: 4px;\\n  }\\n  .border-r {\\n    border-right-style: var(--tw-border-style);\\n    border-right-width: 1px;\\n  }\\n  .border-blue-400 {\\n    border-color: var(--color-blue-400);\\n  }\\n  .border-gray-300 {\\n    border-color: var(--color-gray-300);\\n  }\\n  .border-white {\\n    border-color: var(--color-white);\\n  }\\n  .bg-\\\\[\\\\#81a989\\\\] {\\n    background-color: #81a989;\\n  }\\n  .bg-blue-100 {\\n    background-color: var(--color-blue-100);\\n  }\\n  .bg-gray-50 {\\n    background-color: var(--color-gray-50);\\n  }\\n  .bg-gray-100 {\\n    background-color: var(--color-gray-100);\\n  }\\n  .bg-gray-300 {\\n    background-color: var(--color-gray-300);\\n  }\\n  .bg-green-100 {\\n    background-color: var(--color-green-100);\\n  }\\n  .bg-green-200 {\\n    background-color: var(--color-green-200);\\n  }\\n  .bg-green-800 {\\n    background-color: var(--color-green-800);\\n  }\\n  .bg-green-900 {\\n    background-color: var(--color-green-900);\\n  }\\n  .bg-white {\\n    background-color: var(--color-white);\\n  }\\n  .p-2 {\\n    padding: calc(var(--spacing) * 2);\\n  }\\n  .p-4 {\\n    padding: calc(var(--spacing) * 4);\\n  }\\n  .p-6 {\\n    padding: calc(var(--spacing) * 6);\\n  }\\n  .px-2 {\\n    padding-inline: calc(var(--spacing) * 2);\\n  }\\n  .px-3 {\\n    padding-inline: calc(var(--spacing) * 3);\\n  }\\n  .px-4 {\\n    padding-inline: calc(var(--spacing) * 4);\\n  }\\n  .px-6 {\\n    padding-inline: calc(var(--spacing) * 6);\\n  }\\n  .px-8 {\\n    padding-inline: calc(var(--spacing) * 8);\\n  }\\n  .py-1 {\\n    padding-block: calc(var(--spacing) * 1);\\n  }\\n  .py-2 {\\n    padding-block: calc(var(--spacing) * 2);\\n  }\\n  .py-3 {\\n    padding-block: calc(var(--spacing) * 3);\\n  }\\n  .py-16 {\\n    padding-block: calc(var(--spacing) * 16);\\n  }\\n  .py-20 {\\n    padding-block: calc(var(--spacing) * 20);\\n  }\\n  .pt-20 {\\n    padding-top: calc(var(--spacing) * 20);\\n  }\\n  .pb-8 {\\n    padding-bottom: calc(var(--spacing) * 8);\\n  }\\n  .text-center {\\n    text-align: center;\\n  }\\n  .text-2xl {\\n    font-size: var(--text-2xl);\\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\\n  }\\n  .text-3xl {\\n    font-size: var(--text-3xl);\\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\\n  }\\n  .text-4xl {\\n    font-size: var(--text-4xl);\\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\\n  }\\n  .text-lg {\\n    font-size: var(--text-lg);\\n    line-height: var(--tw-leading, var(--text-lg--line-height));\\n  }\\n  .text-sm {\\n    font-size: var(--text-sm);\\n    line-height: var(--tw-leading, var(--text-sm--line-height));\\n  }\\n  .text-xl {\\n    font-size: var(--text-xl);\\n    line-height: var(--tw-leading, var(--text-xl--line-height));\\n  }\\n  .text-xs {\\n    font-size: var(--text-xs);\\n    line-height: var(--tw-leading, var(--text-xs--line-height));\\n  }\\n  .font-bold {\\n    --tw-font-weight: var(--font-weight-bold);\\n    font-weight: var(--font-weight-bold);\\n  }\\n  .font-medium {\\n    --tw-font-weight: var(--font-weight-medium);\\n    font-weight: var(--font-weight-medium);\\n  }\\n  .font-semibold {\\n    --tw-font-weight: var(--font-weight-semibold);\\n    font-weight: var(--font-weight-semibold);\\n  }\\n  .text-black {\\n    color: var(--color-black);\\n  }\\n  .text-blue-800 {\\n    color: var(--color-blue-800);\\n  }\\n  .text-gray-500 {\\n    color: var(--color-gray-500);\\n  }\\n  .text-gray-600 {\\n    color: var(--color-gray-600);\\n  }\\n  .text-gray-700 {\\n    color: var(--color-gray-700);\\n  }\\n  .text-gray-800 {\\n    color: var(--color-gray-800);\\n  }\\n  .text-gray-900 {\\n    color: var(--color-gray-900);\\n  }\\n  .text-green-800 {\\n    color: var(--color-green-800);\\n  }\\n  .text-green-900 {\\n    color: var(--color-green-900);\\n  }\\n  .text-white {\\n    color: var(--color-white);\\n  }\\n  .capitalize {\\n    text-transform: capitalize;\\n  }\\n  .shadow-md {\\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  }\\n  .transition-colors {\\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .transition-shadow {\\n    transition-property: box-shadow;\\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\\n  }\\n  .duration-200 {\\n    --tw-duration: 200ms;\\n    transition-duration: 200ms;\\n  }\\n  .hover\\\\:bg-\\\\[\\\\#c8d5b9\\\\] {\\n    &:hover {\\n      @media (hover: hover) {\\n        background-color: #c8d5b9;\\n      }\\n    }\\n  }\\n  .hover\\\\:text-gray-800 {\\n    &:hover {\\n      @media (hover: hover) {\\n        color: var(--color-gray-800);\\n      }\\n    }\\n  }\\n  .hover\\\\:shadow-lg {\\n    &:hover {\\n      @media (hover: hover) {\\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n      }\\n    }\\n  }\\n  .md\\\\:grid-cols-2 {\\n    @media (width >= 48rem) {\\n      grid-template-columns: repeat(2, minmax(0, 1fr));\\n    }\\n  }\\n  .md\\\\:grid-cols-3 {\\n    @media (width >= 48rem) {\\n      grid-template-columns: repeat(3, minmax(0, 1fr));\\n    }\\n  }\\n  .lg\\\\:grid-cols-3 {\\n    @media (width >= 64rem) {\\n      grid-template-columns: repeat(3, minmax(0, 1fr));\\n    }\\n  }\\n}\\nbody {\\n  margin: 0;\\n  padding: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n* {\\n  box-sizing: border-box;\\n}\\n@property --tw-space-y-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-space-x-reverse {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0;\\n}\\n@property --tw-border-style {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: solid;\\n}\\n@property --tw-font-weight {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-inset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-shadow-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-shadow-alpha {\\n  syntax: \\\"<percentage>\\\";\\n  inherits: false;\\n  initial-value: 100%;\\n}\\n@property --tw-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-inset-ring-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-inset-ring-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-ring-inset {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@property --tw-ring-offset-width {\\n  syntax: \\\"<length>\\\";\\n  inherits: false;\\n  initial-value: 0px;\\n}\\n@property --tw-ring-offset-color {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: #fff;\\n}\\n@property --tw-ring-offset-shadow {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n  initial-value: 0 0 #0000;\\n}\\n@property --tw-duration {\\n  syntax: \\\"*\\\";\\n  inherits: false;\\n}\\n@layer properties {\\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\\n    *, ::before, ::after, ::backdrop {\\n      --tw-space-y-reverse: 0;\\n      --tw-space-x-reverse: 0;\\n      --tw-border-style: solid;\\n      --tw-font-weight: initial;\\n      --tw-shadow: 0 0 #0000;\\n      --tw-shadow-color: initial;\\n      --tw-shadow-alpha: 100%;\\n      --tw-inset-shadow: 0 0 #0000;\\n      --tw-inset-shadow-color: initial;\\n      --tw-inset-shadow-alpha: 100%;\\n      --tw-ring-color: initial;\\n      --tw-ring-shadow: 0 0 #0000;\\n      --tw-inset-ring-color: initial;\\n      --tw-inset-ring-shadow: 0 0 #0000;\\n      --tw-ring-inset: initial;\\n      --tw-ring-offset-width: 0px;\\n      --tw-ring-offset-color: #fff;\\n      --tw-ring-offset-shadow: 0 0 #0000;\\n      --tw-duration: initial;\\n    }\\n  }\\n}\", \"\",{\"version\":3,\"sources\":[\"<no source>\",\"webpack://node_modules/tailwindcss/index.css\",\"webpack://global.css\"],\"names\":[],\"mappings\":\"AAAA,kEAAA;ACs3BE,iBAAmB;AAt3BrB,yCAAyC;AAEzC;EACE;IACE;6DAEyD;IAEzD;8BAE0B;IA+D1B,6CAA6C;IAC7C,6CAA6C;IAM7C,6CAA6C;IAC7C,6CAA6C;IAoD7C,4CAA4C;IAG5C,4CAA4C;IAI5C,4CAA4C;IAwF5C,2CAA2C;IAC3C,4CAA4C;IAE5C,2CAA2C;IAE3C,4CAA4C;IAC5C,2CAA2C;IAC3C,4CAA4C;IAC5C,4CAA4C;IAC5C,0CAA0C;IAuC1C,mBAAmB;IACnB,mBAAmB;IAEnB,kBAAkB;IAiBlB,sBAAsB;IAEtB,sBAAsB;IAGtB,kBAAkB;IAClB,sCAAsC;IACtC,mBAAmB;IACnB,0CAA0C;IAG1C,mBAAmB;IACnB,0CAA0C;IAC1C,kBAAkB;IAClB,yCAAyC;IACzC,kBAAkB;IAClB,sCAAsC;IACtC,oBAAoB;IACpB,2CAA2C;IAC3C,mBAAmB;IACnB,yCAAyC;IAgBzC,yBAAyB;IACzB,2BAA2B;IAC3B,uBAAuB;IAmBvB,qBAAqB;IACrB,mBAAmB;IAkGnB,oCAAoC;IACpC,kEAAkE;IAClE,uCAAoD;IASpD,4CAAyD;EA5c5C;AADJ;AAmeb;EAOE;IAKE,sBAAsB;IACtB,SAAS;IACT,UAAU;IACV,eAAe;EAJM;EAiBvB;IAEE,gBAAgB;IAChB,8BAA8B;IAC9B,WAAW;IACX,2JASC;IACD,mEAGC;IACD,uEAGC;IACD,wCAAwC;EAtBpC;EA+BN;IACE,SAAS;IACT,cAAc;IACd,qBAAqB;EAHpB;EAUH;IACE,yCAAyC;IACzC,iCAAiC;EAFf;EASpB;IAME,kBAAkB;IAClB,oBAAoB;EAFnB;EASH;IACE,cAAc;IACd,gCAAgC;IAChC,wBAAwB;EAHxB;EAUF;IAEE,mBAAmB;EADd;EAWP;IAIE,gJAUC;IACD,wEAGC;IACD,4EAGC;IACD,cAAc;EApBZ;EA2BJ;IACE,cAAc;EADV;EAQN;IAEE,cAAc;IACd,cAAc;IACd,kBAAkB;IAClB,wBAAwB;EAJtB;EAOJ;IACE,eAAe;EADb;EAIJ;IACE,WAAW;EADT;EAUJ;IACE,cAAc;IACd,qBAAqB;IACrB,yBAAyB;EAHrB;EAUN;IACE,aAAa;EADC;EAQhB;IACE,wBAAwB;EADjB;EAQT;IACE,kBAAkB;EADZ;EAQR;IAGE,gBAAgB;EADb;EAUL;IAQE,cAAc;IACd,sBAAsB;EAFjB;EASP;IAEE,eAAe;IACf,YAAY;EAFR;EAYN;IAME,aAAa;IACb,8BAA8B;IAC9B,gCAAgC;IAChC,uBAAuB;IACvB,cAAc;IACd,gBAAgB;IAChB,6BAA6B;IAC7B,UAAU;EARW;EAevB;IACE,mBAAmB;EAD0B;EAQ/C;IACE,0BAA0B;EAD0B;EAQtD;IACE,sBAAsB;EADD;EAQvB;IACE,UAAU;EADE;EASd;IAEE;MACE,mBAAyD;MAAzD;QAAA,yDAAyD;MAAA;IAD7C;EADiC;EAUjD;IACE,gBAAgB;EADT;EAQT;IACE,wBAAwB;EADE;EAS5B;IACE,eAAe;IACf,mBAAmB;EAFS;EAS9B;IACE,oBAAoB;EADE;EAQxB;IACE,UAAU;EAD2B;EAIvC;IASE,gBAAgB;EADqB;EAQvC;IACE,gBAAgB;EADD;EAQjB;IAGE,kBAAkB;EADG;EAQvB;IAEE,YAAY;EADc;EAQ5B;IACE,wBAAwB;EADmB;AA3YnC;AAgZZ;EACE;IAAA,kBAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,kCAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,WAAmB;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;IAAnB;MAAA,gBAAmB;IAAA;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,qCAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,aAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,WAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,OAAmB;EAAA;EAAnB;IAAA,gDAAmB;EAAA;EAAnB;IAAA,gDAAmB;EAAA;EAAnB;IAAA,eAAmB;EAAA;EAAnB;IAAA,mBAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,8BAAmB;EAAA;EAAnB;IAAA,uBAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,8EAAmB;MAAnB,sFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,+EAAmB;MAAnB,uFAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,uBAAmB;MAAnB,+EAAmB;MAAnB,uFAAmB;IAAA;EAAA;EAAnB;IAAA,gBAAmB;EAAA;EAAnB;IAAA,sBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,+BAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,oCAAmB;IAAnB,iBAAmB;EAAA;EAAnB;IAAA,0CAAmB;IAAnB,uBAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,mCAAmB;EAAA;EAAnB;IAAA,gCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,oCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,iCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,uCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,sCAAmB;EAAA;EAAnB;IAAA,wCAAmB;EAAA;EAAnB;IAAA,kBAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,0BAAmB;IAAnB,4DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yBAAmB;IAAnB,2DAAmB;EAAA;EAAnB;IAAA,yCAAmB;IAAnB,oCAAmB;EAAA;EAAnB;IAAA,2CAAmB;IAAnB,sCAAmB;EAAA;EAAnB;IAAA,6CAAmB;IAAnB,wCAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,4BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,6BAAmB;EAAA;EAAnB;IAAA,yBAAmB;EAAA;EAAnB;IAAA,0BAAmB;EAAA;EAAnB;IAAA,6HAAmB;IAAnB,sIAAmB;EAAA;EAAnB;IAAA,uKAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,+BAAmB;IAAnB,qFAAmB;IAAnB,2EAAmB;EAAA;EAAnB;IAAA,oBAAmB;IAAnB,0BAAmB;EAAA;EAAnB;IAAA;MAAA;QAAA,yBAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,4BAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA;QAAA,+HAAmB;QAAnB,sIAAmB;MAAA;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;EAAnB;IAAA;MAAA,gDAAmB;IAAA;EAAA;AADJ;ACn3BjB;EACE,SAAU;EACV,UAAW;EACX;;cAEa;EACb,mCAAoC;EACpC,kCAAmC;AACpC;AAED;EACE,sBAAuB;AACxB;ADw2BC;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,gBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,oBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,sBAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA,kBAAmB;EAAnB,eAAmB;EAAnB,kBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,mBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;EAAnB,wBAAmB;AAAA;AAAnB;EAAA,WAAmB;EAAnB,eAAmB;AAAA;AAAnB;EAAA;IAAA;MAAA,uBAAmB;MAAnB,uBAAmB;MAAnB,wBAAmB;MAAnB,yBAAmB;MAAnB,sBAAmB;MAAnB,0BAAmB;MAAnB,uBAAmB;MAAnB,4BAAmB;MAAnB,gCAAmB;MAAnB,6BAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,8BAAmB;MAAnB,iCAAmB;MAAnB,wBAAmB;MAAnB,2BAAmB;MAAnB,4BAAmB;MAAnB,kCAAmB;MAAnB,sBAAmB;IAAA;EAAA;AAAA\",\"sourcesContent\":[null,\"@layer theme, base, components, utilities;\\n\\n@layer theme {\\n  @theme default {\\n    --font-sans:\\n      ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\";\\n    --font-serif: ui-serif, Georgia, Cambria, \\\"Times New Roman\\\", Times, serif;\\n    --font-mono:\\n      ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\", monospace;\\n\\n    --color-red-50: oklch(97.1% 0.013 17.38);\\n    --color-red-100: oklch(93.6% 0.032 17.717);\\n    --color-red-200: oklch(88.5% 0.062 18.334);\\n    --color-red-300: oklch(80.8% 0.114 19.571);\\n    --color-red-400: oklch(70.4% 0.191 22.216);\\n    --color-red-500: oklch(63.7% 0.237 25.331);\\n    --color-red-600: oklch(57.7% 0.245 27.325);\\n    --color-red-700: oklch(50.5% 0.213 27.518);\\n    --color-red-800: oklch(44.4% 0.177 26.899);\\n    --color-red-900: oklch(39.6% 0.141 25.723);\\n    --color-red-950: oklch(25.8% 0.092 26.042);\\n\\n    --color-orange-50: oklch(98% 0.016 73.684);\\n    --color-orange-100: oklch(95.4% 0.038 75.164);\\n    --color-orange-200: oklch(90.1% 0.076 70.697);\\n    --color-orange-300: oklch(83.7% 0.128 66.29);\\n    --color-orange-400: oklch(75% 0.183 55.934);\\n    --color-orange-500: oklch(70.5% 0.213 47.604);\\n    --color-orange-600: oklch(64.6% 0.222 41.116);\\n    --color-orange-700: oklch(55.3% 0.195 38.402);\\n    --color-orange-800: oklch(47% 0.157 37.304);\\n    --color-orange-900: oklch(40.8% 0.123 38.172);\\n    --color-orange-950: oklch(26.6% 0.079 36.259);\\n\\n    --color-amber-50: oklch(98.7% 0.022 95.277);\\n    --color-amber-100: oklch(96.2% 0.059 95.617);\\n    --color-amber-200: oklch(92.4% 0.12 95.746);\\n    --color-amber-300: oklch(87.9% 0.169 91.605);\\n    --color-amber-400: oklch(82.8% 0.189 84.429);\\n    --color-amber-500: oklch(76.9% 0.188 70.08);\\n    --color-amber-600: oklch(66.6% 0.179 58.318);\\n    --color-amber-700: oklch(55.5% 0.163 48.998);\\n    --color-amber-800: oklch(47.3% 0.137 46.201);\\n    --color-amber-900: oklch(41.4% 0.112 45.904);\\n    --color-amber-950: oklch(27.9% 0.077 45.635);\\n\\n    --color-yellow-50: oklch(98.7% 0.026 102.212);\\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\\n    --color-yellow-300: oklch(90.5% 0.182 98.111);\\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\\n    --color-yellow-950: oklch(28.6% 0.066 53.813);\\n\\n    --color-lime-50: oklch(98.6% 0.031 120.757);\\n    --color-lime-100: oklch(96.7% 0.067 122.328);\\n    --color-lime-200: oklch(93.8% 0.127 124.321);\\n    --color-lime-300: oklch(89.7% 0.196 126.665);\\n    --color-lime-400: oklch(84.1% 0.238 128.85);\\n    --color-lime-500: oklch(76.8% 0.233 130.85);\\n    --color-lime-600: oklch(64.8% 0.2 131.684);\\n    --color-lime-700: oklch(53.2% 0.157 131.589);\\n    --color-lime-800: oklch(45.3% 0.124 130.933);\\n    --color-lime-900: oklch(40.5% 0.101 131.063);\\n    --color-lime-950: oklch(27.4% 0.072 132.109);\\n\\n    --color-green-50: oklch(98.2% 0.018 155.826);\\n    --color-green-100: oklch(96.2% 0.044 156.743);\\n    --color-green-200: oklch(92.5% 0.084 155.995);\\n    --color-green-300: oklch(87.1% 0.15 154.449);\\n    --color-green-400: oklch(79.2% 0.209 151.711);\\n    --color-green-500: oklch(72.3% 0.219 149.579);\\n    --color-green-600: oklch(62.7% 0.194 149.214);\\n    --color-green-700: oklch(52.7% 0.154 150.069);\\n    --color-green-800: oklch(44.8% 0.119 151.328);\\n    --color-green-900: oklch(39.3% 0.095 152.535);\\n    --color-green-950: oklch(26.6% 0.065 152.934);\\n\\n    --color-emerald-50: oklch(97.9% 0.021 166.113);\\n    --color-emerald-100: oklch(95% 0.052 163.051);\\n    --color-emerald-200: oklch(90.5% 0.093 164.15);\\n    --color-emerald-300: oklch(84.5% 0.143 164.978);\\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\\n    --color-emerald-600: oklch(59.6% 0.145 163.225);\\n    --color-emerald-700: oklch(50.8% 0.118 165.612);\\n    --color-emerald-800: oklch(43.2% 0.095 166.913);\\n    --color-emerald-900: oklch(37.8% 0.077 168.94);\\n    --color-emerald-950: oklch(26.2% 0.051 172.552);\\n\\n    --color-teal-50: oklch(98.4% 0.014 180.72);\\n    --color-teal-100: oklch(95.3% 0.051 180.801);\\n    --color-teal-200: oklch(91% 0.096 180.426);\\n    --color-teal-300: oklch(85.5% 0.138 181.071);\\n    --color-teal-400: oklch(77.7% 0.152 181.912);\\n    --color-teal-500: oklch(70.4% 0.14 182.503);\\n    --color-teal-600: oklch(60% 0.118 184.704);\\n    --color-teal-700: oklch(51.1% 0.096 186.391);\\n    --color-teal-800: oklch(43.7% 0.078 188.216);\\n    --color-teal-900: oklch(38.6% 0.063 188.416);\\n    --color-teal-950: oklch(27.7% 0.046 192.524);\\n\\n    --color-cyan-50: oklch(98.4% 0.019 200.873);\\n    --color-cyan-100: oklch(95.6% 0.045 203.388);\\n    --color-cyan-200: oklch(91.7% 0.08 205.041);\\n    --color-cyan-300: oklch(86.5% 0.127 207.078);\\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\\n    --color-cyan-600: oklch(60.9% 0.126 221.723);\\n    --color-cyan-700: oklch(52% 0.105 223.128);\\n    --color-cyan-800: oklch(45% 0.085 224.283);\\n    --color-cyan-900: oklch(39.8% 0.07 227.392);\\n    --color-cyan-950: oklch(30.2% 0.056 229.695);\\n\\n    --color-sky-50: oklch(97.7% 0.013 236.62);\\n    --color-sky-100: oklch(95.1% 0.026 236.824);\\n    --color-sky-200: oklch(90.1% 0.058 230.902);\\n    --color-sky-300: oklch(82.8% 0.111 230.318);\\n    --color-sky-400: oklch(74.6% 0.16 232.661);\\n    --color-sky-500: oklch(68.5% 0.169 237.323);\\n    --color-sky-600: oklch(58.8% 0.158 241.966);\\n    --color-sky-700: oklch(50% 0.134 242.749);\\n    --color-sky-800: oklch(44.3% 0.11 240.79);\\n    --color-sky-900: oklch(39.1% 0.09 240.876);\\n    --color-sky-950: oklch(29.3% 0.066 243.157);\\n\\n    --color-blue-50: oklch(97% 0.014 254.604);\\n    --color-blue-100: oklch(93.2% 0.032 255.585);\\n    --color-blue-200: oklch(88.2% 0.059 254.128);\\n    --color-blue-300: oklch(80.9% 0.105 251.813);\\n    --color-blue-400: oklch(70.7% 0.165 254.624);\\n    --color-blue-500: oklch(62.3% 0.214 259.815);\\n    --color-blue-600: oklch(54.6% 0.245 262.881);\\n    --color-blue-700: oklch(48.8% 0.243 264.376);\\n    --color-blue-800: oklch(42.4% 0.199 265.638);\\n    --color-blue-900: oklch(37.9% 0.146 265.522);\\n    --color-blue-950: oklch(28.2% 0.091 267.935);\\n\\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\\n    --color-indigo-100: oklch(93% 0.034 272.788);\\n    --color-indigo-200: oklch(87% 0.065 274.039);\\n    --color-indigo-300: oklch(78.5% 0.115 274.713);\\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\\n    --color-indigo-700: oklch(45.7% 0.24 277.023);\\n    --color-indigo-800: oklch(39.8% 0.195 277.366);\\n    --color-indigo-900: oklch(35.9% 0.144 278.697);\\n    --color-indigo-950: oklch(25.7% 0.09 281.288);\\n\\n    --color-violet-50: oklch(96.9% 0.016 293.756);\\n    --color-violet-100: oklch(94.3% 0.029 294.588);\\n    --color-violet-200: oklch(89.4% 0.057 293.283);\\n    --color-violet-300: oklch(81.1% 0.111 293.571);\\n    --color-violet-400: oklch(70.2% 0.183 293.541);\\n    --color-violet-500: oklch(60.6% 0.25 292.717);\\n    --color-violet-600: oklch(54.1% 0.281 293.009);\\n    --color-violet-700: oklch(49.1% 0.27 292.581);\\n    --color-violet-800: oklch(43.2% 0.232 292.759);\\n    --color-violet-900: oklch(38% 0.189 293.745);\\n    --color-violet-950: oklch(28.3% 0.141 291.089);\\n\\n    --color-purple-50: oklch(97.7% 0.014 308.299);\\n    --color-purple-100: oklch(94.6% 0.033 307.174);\\n    --color-purple-200: oklch(90.2% 0.063 306.703);\\n    --color-purple-300: oklch(82.7% 0.119 306.383);\\n    --color-purple-400: oklch(71.4% 0.203 305.504);\\n    --color-purple-500: oklch(62.7% 0.265 303.9);\\n    --color-purple-600: oklch(55.8% 0.288 302.321);\\n    --color-purple-700: oklch(49.6% 0.265 301.924);\\n    --color-purple-800: oklch(43.8% 0.218 303.724);\\n    --color-purple-900: oklch(38.1% 0.176 304.987);\\n    --color-purple-950: oklch(29.1% 0.149 302.717);\\n\\n    --color-fuchsia-50: oklch(97.7% 0.017 320.058);\\n    --color-fuchsia-100: oklch(95.2% 0.037 318.852);\\n    --color-fuchsia-200: oklch(90.3% 0.076 319.62);\\n    --color-fuchsia-300: oklch(83.3% 0.145 321.434);\\n    --color-fuchsia-400: oklch(74% 0.238 322.16);\\n    --color-fuchsia-500: oklch(66.7% 0.295 322.15);\\n    --color-fuchsia-600: oklch(59.1% 0.293 322.896);\\n    --color-fuchsia-700: oklch(51.8% 0.253 323.949);\\n    --color-fuchsia-800: oklch(45.2% 0.211 324.591);\\n    --color-fuchsia-900: oklch(40.1% 0.17 325.612);\\n    --color-fuchsia-950: oklch(29.3% 0.136 325.661);\\n\\n    --color-pink-50: oklch(97.1% 0.014 343.198);\\n    --color-pink-100: oklch(94.8% 0.028 342.258);\\n    --color-pink-200: oklch(89.9% 0.061 343.231);\\n    --color-pink-300: oklch(82.3% 0.12 346.018);\\n    --color-pink-400: oklch(71.8% 0.202 349.761);\\n    --color-pink-500: oklch(65.6% 0.241 354.308);\\n    --color-pink-600: oklch(59.2% 0.249 0.584);\\n    --color-pink-700: oklch(52.5% 0.223 3.958);\\n    --color-pink-800: oklch(45.9% 0.187 3.815);\\n    --color-pink-900: oklch(40.8% 0.153 2.432);\\n    --color-pink-950: oklch(28.4% 0.109 3.907);\\n\\n    --color-rose-50: oklch(96.9% 0.015 12.422);\\n    --color-rose-100: oklch(94.1% 0.03 12.58);\\n    --color-rose-200: oklch(89.2% 0.058 10.001);\\n    --color-rose-300: oklch(81% 0.117 11.638);\\n    --color-rose-400: oklch(71.2% 0.194 13.428);\\n    --color-rose-500: oklch(64.5% 0.246 16.439);\\n    --color-rose-600: oklch(58.6% 0.253 17.585);\\n    --color-rose-700: oklch(51.4% 0.222 16.935);\\n    --color-rose-800: oklch(45.5% 0.188 13.697);\\n    --color-rose-900: oklch(41% 0.159 10.272);\\n    --color-rose-950: oklch(27.1% 0.105 12.094);\\n\\n    --color-slate-50: oklch(98.4% 0.003 247.858);\\n    --color-slate-100: oklch(96.8% 0.007 247.896);\\n    --color-slate-200: oklch(92.9% 0.013 255.508);\\n    --color-slate-300: oklch(86.9% 0.022 252.894);\\n    --color-slate-400: oklch(70.4% 0.04 256.788);\\n    --color-slate-500: oklch(55.4% 0.046 257.417);\\n    --color-slate-600: oklch(44.6% 0.043 257.281);\\n    --color-slate-700: oklch(37.2% 0.044 257.287);\\n    --color-slate-800: oklch(27.9% 0.041 260.031);\\n    --color-slate-900: oklch(20.8% 0.042 265.755);\\n    --color-slate-950: oklch(12.9% 0.042 264.695);\\n\\n    --color-gray-50: oklch(98.5% 0.002 247.839);\\n    --color-gray-100: oklch(96.7% 0.003 264.542);\\n    --color-gray-200: oklch(92.8% 0.006 264.531);\\n    --color-gray-300: oklch(87.2% 0.01 258.338);\\n    --color-gray-400: oklch(70.7% 0.022 261.325);\\n    --color-gray-500: oklch(55.1% 0.027 264.364);\\n    --color-gray-600: oklch(44.6% 0.03 256.802);\\n    --color-gray-700: oklch(37.3% 0.034 259.733);\\n    --color-gray-800: oklch(27.8% 0.033 256.848);\\n    --color-gray-900: oklch(21% 0.034 264.665);\\n    --color-gray-950: oklch(13% 0.028 261.692);\\n\\n    --color-zinc-50: oklch(98.5% 0 0);\\n    --color-zinc-100: oklch(96.7% 0.001 286.375);\\n    --color-zinc-200: oklch(92% 0.004 286.32);\\n    --color-zinc-300: oklch(87.1% 0.006 286.286);\\n    --color-zinc-400: oklch(70.5% 0.015 286.067);\\n    --color-zinc-500: oklch(55.2% 0.016 285.938);\\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\\n    --color-zinc-700: oklch(37% 0.013 285.805);\\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\\n    --color-zinc-900: oklch(21% 0.006 285.885);\\n    --color-zinc-950: oklch(14.1% 0.005 285.823);\\n\\n    --color-neutral-50: oklch(98.5% 0 0);\\n    --color-neutral-100: oklch(97% 0 0);\\n    --color-neutral-200: oklch(92.2% 0 0);\\n    --color-neutral-300: oklch(87% 0 0);\\n    --color-neutral-400: oklch(70.8% 0 0);\\n    --color-neutral-500: oklch(55.6% 0 0);\\n    --color-neutral-600: oklch(43.9% 0 0);\\n    --color-neutral-700: oklch(37.1% 0 0);\\n    --color-neutral-800: oklch(26.9% 0 0);\\n    --color-neutral-900: oklch(20.5% 0 0);\\n    --color-neutral-950: oklch(14.5% 0 0);\\n\\n    --color-stone-50: oklch(98.5% 0.001 106.423);\\n    --color-stone-100: oklch(97% 0.001 106.424);\\n    --color-stone-200: oklch(92.3% 0.003 48.717);\\n    --color-stone-300: oklch(86.9% 0.005 56.366);\\n    --color-stone-400: oklch(70.9% 0.01 56.259);\\n    --color-stone-500: oklch(55.3% 0.013 58.071);\\n    --color-stone-600: oklch(44.4% 0.011 73.639);\\n    --color-stone-700: oklch(37.4% 0.01 67.558);\\n    --color-stone-800: oklch(26.8% 0.007 34.298);\\n    --color-stone-900: oklch(21.6% 0.006 56.043);\\n    --color-stone-950: oklch(14.7% 0.004 49.25);\\n\\n    --color-black: #000;\\n    --color-white: #fff;\\n\\n    --spacing: 0.25rem;\\n\\n    --breakpoint-sm: 40rem;\\n    --breakpoint-md: 48rem;\\n    --breakpoint-lg: 64rem;\\n    --breakpoint-xl: 80rem;\\n    --breakpoint-2xl: 96rem;\\n\\n    --container-3xs: 16rem;\\n    --container-2xs: 18rem;\\n    --container-xs: 20rem;\\n    --container-sm: 24rem;\\n    --container-md: 28rem;\\n    --container-lg: 32rem;\\n    --container-xl: 36rem;\\n    --container-2xl: 42rem;\\n    --container-3xl: 48rem;\\n    --container-4xl: 56rem;\\n    --container-5xl: 64rem;\\n    --container-6xl: 72rem;\\n    --container-7xl: 80rem;\\n\\n    --text-xs: 0.75rem;\\n    --text-xs--line-height: calc(1 / 0.75);\\n    --text-sm: 0.875rem;\\n    --text-sm--line-height: calc(1.25 / 0.875);\\n    --text-base: 1rem;\\n    --text-base--line-height: calc(1.5 / 1);\\n    --text-lg: 1.125rem;\\n    --text-lg--line-height: calc(1.75 / 1.125);\\n    --text-xl: 1.25rem;\\n    --text-xl--line-height: calc(1.75 / 1.25);\\n    --text-2xl: 1.5rem;\\n    --text-2xl--line-height: calc(2 / 1.5);\\n    --text-3xl: 1.875rem;\\n    --text-3xl--line-height: calc(2.25 / 1.875);\\n    --text-4xl: 2.25rem;\\n    --text-4xl--line-height: calc(2.5 / 2.25);\\n    --text-5xl: 3rem;\\n    --text-5xl--line-height: 1;\\n    --text-6xl: 3.75rem;\\n    --text-6xl--line-height: 1;\\n    --text-7xl: 4.5rem;\\n    --text-7xl--line-height: 1;\\n    --text-8xl: 6rem;\\n    --text-8xl--line-height: 1;\\n    --text-9xl: 8rem;\\n    --text-9xl--line-height: 1;\\n\\n    --font-weight-thin: 100;\\n    --font-weight-extralight: 200;\\n    --font-weight-light: 300;\\n    --font-weight-normal: 400;\\n    --font-weight-medium: 500;\\n    --font-weight-semibold: 600;\\n    --font-weight-bold: 700;\\n    --font-weight-extrabold: 800;\\n    --font-weight-black: 900;\\n\\n    --tracking-tighter: -0.05em;\\n    --tracking-tight: -0.025em;\\n    --tracking-normal: 0em;\\n    --tracking-wide: 0.025em;\\n    --tracking-wider: 0.05em;\\n    --tracking-widest: 0.1em;\\n\\n    --leading-tight: 1.25;\\n    --leading-snug: 1.375;\\n    --leading-normal: 1.5;\\n    --leading-relaxed: 1.625;\\n    --leading-loose: 2;\\n\\n    --radius-xs: 0.125rem;\\n    --radius-sm: 0.25rem;\\n    --radius-md: 0.375rem;\\n    --radius-lg: 0.5rem;\\n    --radius-xl: 0.75rem;\\n    --radius-2xl: 1rem;\\n    --radius-3xl: 1.5rem;\\n    --radius-4xl: 2rem;\\n\\n    --shadow-2xs: 0 1px rgb(0 0 0 / 0.05);\\n    --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n    --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-md:\\n      0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n    --shadow-lg:\\n      0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl:\\n      0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);\\n\\n    --inset-shadow-2xs: inset 0 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-xs: inset 0 1px 1px rgb(0 0 0 / 0.05);\\n    --inset-shadow-sm: inset 0 2px 4px rgb(0 0 0 / 0.05);\\n\\n    --drop-shadow-xs: 0 1px 1px rgb(0 0 0 / 0.05);\\n    --drop-shadow-sm: 0 1px 2px rgb(0 0 0 / 0.15);\\n    --drop-shadow-md: 0 3px 3px rgb(0 0 0 / 0.12);\\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\\n    --drop-shadow-xl: 0 9px 7px rgb(0 0 0 / 0.1);\\n    --drop-shadow-2xl: 0 25px 25px rgb(0 0 0 / 0.15);\\n\\n    --text-shadow-2xs: 0px 1px 0px rgb(0 0 0 / 0.15);\\n    --text-shadow-xs: 0px 1px 1px rgb(0 0 0 / 0.2);\\n    --text-shadow-sm:\\n      0px 1px 0px rgb(0 0 0 / 0.075), 0px 1px 1px rgb(0 0 0 / 0.075),\\n      0px 2px 2px rgb(0 0 0 / 0.075);\\n    --text-shadow-md:\\n      0px 1px 1px rgb(0 0 0 / 0.1), 0px 1px 2px rgb(0 0 0 / 0.1),\\n      0px 2px 4px rgb(0 0 0 / 0.1);\\n    --text-shadow-lg:\\n      0px 1px 2px rgb(0 0 0 / 0.1), 0px 3px 2px rgb(0 0 0 / 0.1),\\n      0px 4px 8px rgb(0 0 0 / 0.1);\\n\\n    --ease-in: cubic-bezier(0.4, 0, 1, 1);\\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\\n\\n    --animate-spin: spin 1s linear infinite;\\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n    --animate-bounce: bounce 1s infinite;\\n\\n    @keyframes spin {\\n      to {\\n        transform: rotate(360deg);\\n      }\\n    }\\n\\n    @keyframes ping {\\n      75%,\\n      100% {\\n        transform: scale(2);\\n        opacity: 0;\\n      }\\n    }\\n\\n    @keyframes pulse {\\n      50% {\\n        opacity: 0.5;\\n      }\\n    }\\n\\n    @keyframes bounce {\\n      0%,\\n      100% {\\n        transform: translateY(-25%);\\n        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);\\n      }\\n\\n      50% {\\n        transform: none;\\n        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\\n      }\\n    }\\n\\n    --blur-xs: 4px;\\n    --blur-sm: 8px;\\n    --blur-md: 12px;\\n    --blur-lg: 16px;\\n    --blur-xl: 24px;\\n    --blur-2xl: 40px;\\n    --blur-3xl: 64px;\\n\\n    --perspective-dramatic: 100px;\\n    --perspective-near: 300px;\\n    --perspective-normal: 500px;\\n    --perspective-midrange: 800px;\\n    --perspective-distant: 1200px;\\n\\n    --aspect-video: 16 / 9;\\n\\n    --default-transition-duration: 150ms;\\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n    --default-font-family: --theme(--font-sans, initial);\\n    --default-font-feature-settings: --theme(\\n      --font-sans--font-feature-settings,\\n      initial\\n    );\\n    --default-font-variation-settings: --theme(\\n      --font-sans--font-variation-settings,\\n      initial\\n    );\\n    --default-mono-font-family: --theme(--font-mono, initial);\\n    --default-mono-font-feature-settings: --theme(\\n      --font-mono--font-feature-settings,\\n      initial\\n    );\\n    --default-mono-font-variation-settings: --theme(\\n      --font-mono--font-variation-settings,\\n      initial\\n    );\\n  }\\n\\n  /* Deprecated */\\n  @theme default inline reference {\\n    --blur: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);\\n    --drop-shadow: 0 1px 2px rgb(0 0 0 / 0.1), 0 1px 1px rgb(0 0 0 / 0.06);\\n    --radius: 0.25rem;\\n    --max-width-prose: 65ch;\\n  }\\n}\\n\\n@layer base {\\n  /*\\n  1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n  2. Remove default margins and padding\\n  3. Reset all borders.\\n*/\\n\\n  *,\\n  ::after,\\n  ::before,\\n  ::backdrop,\\n  ::file-selector-button {\\n    box-sizing: border-box; /* 1 */\\n    margin: 0; /* 2 */\\n    padding: 0; /* 2 */\\n    border: 0 solid; /* 3 */\\n  }\\n\\n  /*\\n  1. Use a consistent sensible line-height in all browsers.\\n  2. Prevent adjustments of font size after orientation changes in iOS.\\n  3. Use a more readable tab size.\\n  4. Use the user's configured `sans` font-family by default.\\n  5. Use the user's configured `sans` font-feature-settings by default.\\n  6. Use the user's configured `sans` font-variation-settings by default.\\n  7. Disable tap highlights on iOS.\\n*/\\n\\n  html,\\n  :host {\\n    line-height: 1.5; /* 1 */\\n    -webkit-text-size-adjust: 100%; /* 2 */\\n    tab-size: 4; /* 3 */\\n    font-family: --theme(\\n      --default-font-family,\\n      ui-sans-serif,\\n      system-ui,\\n      sans-serif,\\n      \\\"Apple Color Emoji\\\",\\n      \\\"Segoe UI Emoji\\\",\\n      \\\"Segoe UI Symbol\\\",\\n      \\\"Noto Color Emoji\\\"\\n    ); /* 4 */\\n    font-feature-settings: --theme(\\n      --default-font-feature-settings,\\n      normal\\n    ); /* 5 */\\n    font-variation-settings: --theme(\\n      --default-font-variation-settings,\\n      normal\\n    ); /* 6 */\\n    -webkit-tap-highlight-color: transparent; /* 7 */\\n  }\\n\\n  /*\\n  1. Add the correct height in Firefox.\\n  2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n  3. Reset the default border style to a 1px solid border.\\n*/\\n\\n  hr {\\n    height: 0; /* 1 */\\n    color: inherit; /* 2 */\\n    border-top-width: 1px; /* 3 */\\n  }\\n\\n  /*\\n  Add the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\n  abbr:where([title]) {\\n    -webkit-text-decoration: underline dotted;\\n    text-decoration: underline dotted;\\n  }\\n\\n  /*\\n  Remove the default font size and weight for headings.\\n*/\\n\\n  h1,\\n  h2,\\n  h3,\\n  h4,\\n  h5,\\n  h6 {\\n    font-size: inherit;\\n    font-weight: inherit;\\n  }\\n\\n  /*\\n  Reset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\n  a {\\n    color: inherit;\\n    -webkit-text-decoration: inherit;\\n    text-decoration: inherit;\\n  }\\n\\n  /*\\n  Add the correct font weight in Edge and Safari.\\n*/\\n\\n  b,\\n  strong {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  1. Use the user's configured `mono` font-family by default.\\n  2. Use the user's configured `mono` font-feature-settings by default.\\n  3. Use the user's configured `mono` font-variation-settings by default.\\n  4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\n  code,\\n  kbd,\\n  samp,\\n  pre {\\n    font-family: --theme(\\n      --default-mono-font-family,\\n      ui-monospace,\\n      SFMono-Regular,\\n      Menlo,\\n      Monaco,\\n      Consolas,\\n      \\\"Liberation Mono\\\",\\n      \\\"Courier New\\\",\\n      monospace\\n    ); /* 1 */\\n    font-feature-settings: --theme(\\n      --default-mono-font-feature-settings,\\n      normal\\n    ); /* 2 */\\n    font-variation-settings: --theme(\\n      --default-mono-font-variation-settings,\\n      normal\\n    ); /* 3 */\\n    font-size: 1em; /* 4 */\\n  }\\n\\n  /*\\n  Add the correct font size in all browsers.\\n*/\\n\\n  small {\\n    font-size: 80%;\\n  }\\n\\n  /*\\n  Prevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\n  sub,\\n  sup {\\n    font-size: 75%;\\n    line-height: 0;\\n    position: relative;\\n    vertical-align: baseline;\\n  }\\n\\n  sub {\\n    bottom: -0.25em;\\n  }\\n\\n  sup {\\n    top: -0.5em;\\n  }\\n\\n  /*\\n  1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n  2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n  3. Remove gaps between table borders by default.\\n*/\\n\\n  table {\\n    text-indent: 0; /* 1 */\\n    border-color: inherit; /* 2 */\\n    border-collapse: collapse; /* 3 */\\n  }\\n\\n  /*\\n  Use the modern Firefox focus style for all focusable elements.\\n*/\\n\\n  :-moz-focusring {\\n    outline: auto;\\n  }\\n\\n  /*\\n  Add the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\n  progress {\\n    vertical-align: baseline;\\n  }\\n\\n  /*\\n  Add the correct display in Chrome and Safari.\\n*/\\n\\n  summary {\\n    display: list-item;\\n  }\\n\\n  /*\\n  Make lists unstyled by default.\\n*/\\n\\n  ol,\\n  ul,\\n  menu {\\n    list-style: none;\\n  }\\n\\n  /*\\n  1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n  2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n      This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\n  img,\\n  svg,\\n  video,\\n  canvas,\\n  audio,\\n  iframe,\\n  embed,\\n  object {\\n    display: block; /* 1 */\\n    vertical-align: middle; /* 2 */\\n  }\\n\\n  /*\\n  Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\n  img,\\n  video {\\n    max-width: 100%;\\n    height: auto;\\n  }\\n\\n  /*\\n  1. Inherit font styles in all browsers.\\n  2. Remove border radius in all browsers.\\n  3. Remove background color in all browsers.\\n  4. Ensure consistent opacity for disabled states in all browsers.\\n*/\\n\\n  button,\\n  input,\\n  select,\\n  optgroup,\\n  textarea,\\n  ::file-selector-button {\\n    font: inherit; /* 1 */\\n    font-feature-settings: inherit; /* 1 */\\n    font-variation-settings: inherit; /* 1 */\\n    letter-spacing: inherit; /* 1 */\\n    color: inherit; /* 1 */\\n    border-radius: 0; /* 2 */\\n    background-color: transparent; /* 3 */\\n    opacity: 1; /* 4 */\\n  }\\n\\n  /*\\n  Restore default font weight.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup {\\n    font-weight: bolder;\\n  }\\n\\n  /*\\n  Restore indentation.\\n*/\\n\\n  :where(select:is([multiple], [size])) optgroup option {\\n    padding-inline-start: 20px;\\n  }\\n\\n  /*\\n  Restore space after button.\\n*/\\n\\n  ::file-selector-button {\\n    margin-inline-end: 4px;\\n  }\\n\\n  /*\\n  Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n*/\\n\\n  ::placeholder {\\n    opacity: 1;\\n  }\\n\\n  /*\\n  Set the default placeholder color to a semi-transparent version of the current text color in browsers that do not\\n  crash when using `color-mix(…)` with `currentcolor`. (https://github.com/tailwindlabs/tailwindcss/issues/17194)\\n*/\\n\\n  @supports (not (-webkit-appearance: -apple-pay-button)) /* Not Safari */ or\\n    (contain-intrinsic-size: 1px) /* Safari 17+ */ {\\n    ::placeholder {\\n      color: color-mix(in oklab, currentcolor 50%, transparent);\\n    }\\n  }\\n\\n  /*\\n  Prevent resizing textareas horizontally by default.\\n*/\\n\\n  textarea {\\n    resize: vertical;\\n  }\\n\\n  /*\\n  Remove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n  ::-webkit-search-decoration {\\n    -webkit-appearance: none;\\n  }\\n\\n  /*\\n  1. Ensure date/time inputs have the same height when empty in iOS Safari.\\n  2. Ensure text alignment can be changed on date/time inputs in iOS Safari.\\n*/\\n\\n  ::-webkit-date-and-time-value {\\n    min-height: 1lh; /* 1 */\\n    text-align: inherit; /* 2 */\\n  }\\n\\n  /*\\n  Prevent height from changing on date/time inputs in macOS Safari when the input is set to `display: block`.\\n*/\\n\\n  ::-webkit-datetime-edit {\\n    display: inline-flex;\\n  }\\n\\n  /*\\n  Remove excess padding from pseudo-elements in date/time inputs to ensure consistent height across browsers.\\n*/\\n\\n  ::-webkit-datetime-edit-fields-wrapper {\\n    padding: 0;\\n  }\\n\\n  ::-webkit-datetime-edit,\\n  ::-webkit-datetime-edit-year-field,\\n  ::-webkit-datetime-edit-month-field,\\n  ::-webkit-datetime-edit-day-field,\\n  ::-webkit-datetime-edit-hour-field,\\n  ::-webkit-datetime-edit-minute-field,\\n  ::-webkit-datetime-edit-second-field,\\n  ::-webkit-datetime-edit-millisecond-field,\\n  ::-webkit-datetime-edit-meridiem-field {\\n    padding-block: 0;\\n  }\\n\\n  /*\\n  Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n  :-moz-ui-invalid {\\n    box-shadow: none;\\n  }\\n\\n  /*\\n  Correct the inability to style the border radius in iOS Safari.\\n*/\\n\\n  button,\\n  input:where([type=\\\"button\\\"], [type=\\\"reset\\\"], [type=\\\"submit\\\"]),\\n  ::file-selector-button {\\n    appearance: button;\\n  }\\n\\n  /*\\n  Correct the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n  ::-webkit-inner-spin-button,\\n  ::-webkit-outer-spin-button {\\n    height: auto;\\n  }\\n\\n  /*\\n  Make elements with the HTML hidden attribute stay hidden by default.\\n*/\\n\\n  [hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n    display: none !important;\\n  }\\n}\\n\\n@layer utilities {\\n  @tailwind utilities;\\n}\\n\",\"@import 'tailwindcss';\\n\\nbody {\\n  margin: 0;\\n  padding: 0;\\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\\n    sans-serif;\\n  -webkit-font-smoothing: antialiased;\\n  -moz-osx-font-smoothing: grayscale;\\n}\\n\\n* {\\n  box-sizing: border-box;\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[13].use[2]!./global.css\n"));

/***/ })

});