"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nvar _s = $RefreshSig$();\nconst Navigation = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = useState('/');\n    const isActive = (path)=>activeTab === path;\n    const handleNavClick = (path)=>{\n        setActiveTab(path);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 px-4 py-3\",\n        style: {\n            backgroundColor: '#4a7c59'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-white text-xl font-bold mr-auto\",\n                    children: \"Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Home\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/protests'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/protests') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/protests') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/protests') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Protests\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/map'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/map') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/map') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/map') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Map\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/submit'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/submit') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/submit') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/submit') ? '#4a7c59' : 'white'\n                    },\n                    children: \"Submit\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>handleNavClick('/about'),\n                    className: \"text-white px-4 py-2 rounded transition-colors \".concat(isActive('/about') ? '' : 'hover:opacity-80'),\n                    style: {\n                        backgroundColor: isActive('/about') ? '#c8d5b9' : 'transparent',\n                        color: isActive('/about') ? '#4a7c59' : 'white'\n                    },\n                    children: \"About\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 15,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"Gdpe0Mf57FCH0960YRQIICVKaDQ=\");\n_c = Navigation;\n\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ })

});