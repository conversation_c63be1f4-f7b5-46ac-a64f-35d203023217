"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "(pages-dir-browser)/./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst Home = ()=>{\n    _s();\n    const [recentProtests, setRecentProtests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            setRecentProtests([\n                {\n                    id: 1,\n                    title: \"Climate Action March\",\n                    location: \"New York, USA\",\n                    date: \"2024-03-15\",\n                    participants: \"10,000+\",\n                    status: \"ended\"\n                },\n                {\n                    id: 2,\n                    title: \"Workers Rights Demonstration\",\n                    location: \"Berlin, Germany\",\n                    date: \"2024-03-18\",\n                    participants: \"5,000+\",\n                    status: \"ongoing\"\n                },\n                {\n                    id: 3,\n                    title: \"Anti-War Protest\",\n                    location: \"London, UK\",\n                    date: \"2024-03-20\",\n                    participants: \"15,000+\",\n                    status: \"planned\"\n                }\n            ]);\n        }\n    }[\"Home.useEffect\"], []);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ongoing':\n                return 'bg-green-100 text-green-800';\n            case 'ended':\n                return 'bg-gray-100 text-gray-800';\n            case 'planned':\n                return 'bg-blue-100 text-blue-800';\n            default:\n                return 'bg-gray-100 text-gray-800';\n        }\n    };\n    const getStatusBgColor = (status)=>{\n        switch(status){\n            case 'ongoing':\n                return '#4a7c59';\n            case 'ended':\n                return '#81a989';\n            case 'planned':\n                return '#c8d5b9';\n            default:\n                return '#81a989';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        style: {\n            backgroundColor: '#c8d5b9'\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"text-white py-20 pt-32\",\n                style: {\n                    background: \"linear-gradient(135deg, #4a7c59 0%, #81a989 100%)\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: \"./assets/Earth.png\",\n                    alt: \"\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        style: {\n                                            color: '#4a7c59'\n                                        },\n                                        children: \"1,247\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Total Events Tracked\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        style: {\n                                            color: '#81a989'\n                                        },\n                                        children: \"89\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Countries Covered\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-4xl font-bold mb-2\",\n                                        style: {\n                                            color: '#4a7c59'\n                                        },\n                                        children: \"23\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Active Today\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold mb-8 text-center\",\n                            style: {\n                                color: '#4a7c59'\n                            },\n                            children: \"Recent Protests\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-6 md:grid-cols-2 lg:grid-cols-3\",\n                            children: recentProtests.map((protest)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold\",\n                                                    style: {\n                                                        color: '#4a7c59'\n                                                    },\n                                                    children: protest.title\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"px-3 py-1 rounded-full text-sm font-medium \".concat(getStatusColor(protest.status)),\n                                                    style: {\n                                                        backgroundColor: getStatusBgColor(protest.status)\n                                                    },\n                                                    children: protest.status\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Location:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2\",\n                                                            children: protest.location\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                            lineNumber: 108,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Date:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                            lineNumber: 111,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2\",\n                                                            children: new Date(protest.date).toLocaleDateString()\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                            lineNumber: 112,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                    lineNumber: 110,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Participants:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                            lineNumber: 115,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2\",\n                                                            children: protest.participants\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, protest.id, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/index.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Home, \"iSm0WD9zFLlwdbDNpu17r8Izb9o=\");\n_c = Home;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Home);\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/index.tsx\n"));

/***/ })

});