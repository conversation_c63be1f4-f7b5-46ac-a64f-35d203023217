"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/feed",{

/***/ "(pages-dir-browser)/./pages/feed.tsx":
/*!************************!*\
  !*** ./pages/feed.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Feed = ()=>{\n    // Sample feed data\n    const feedItems = [\n        {\n            id: 1,\n            type: 'protest',\n            title: 'Climate Action Rally in Berlin',\n            location: 'Berlin, Germany',\n            date: '2024-07-15',\n            participants: 15000,\n            status: 'ongoing',\n            description: 'Thousands gather to demand stronger climate policies...',\n            tags: [\n                'climate',\n                'environment',\n                'policy'\n            ],\n            image: '/api/placeholder/300/200'\n        },\n        {\n            id: 2,\n            type: 'update',\n            title: 'Labor Strike Update: Day 3',\n            location: 'Detroit, MI, USA',\n            date: '2024-07-14',\n            participants: 8500,\n            status: 'ongoing',\n            description: 'Auto workers continue strike for better working conditions...',\n            tags: [\n                'labor',\n                'workers rights',\n                'union'\n            ],\n            image: '/api/placeholder/300/200'\n        },\n        {\n            id: 3,\n            type: 'protest',\n            title: 'Student Protest for Education Reform',\n            location: 'São Paulo, Brazil',\n            date: '2024-07-13',\n            participants: 12000,\n            status: 'completed',\n            description: 'University students demand affordable education...',\n            tags: [\n                'education',\n                'students',\n                'reform'\n            ],\n            image: '/api/placeholder/300/200'\n        },\n        {\n            id: 4,\n            type: 'alert',\n            title: 'Peaceful Demonstration Planned',\n            location: 'Paris, France',\n            date: '2024-07-16',\n            participants: 5000,\n            status: 'planned',\n            description: 'Citizens organizing peaceful march for social justice...',\n            tags: [\n                'social justice',\n                'peaceful',\n                'march'\n            ],\n            image: '/api/placeholder/300/200'\n        }\n    ];\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'ongoing':\n                return '#4a7c59';\n            case 'completed':\n                return '#81a989';\n            case 'planned':\n                return '#c8d5b9';\n            default:\n                return '#4a7c59';\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case 'protest':\n                return '📢';\n            case 'update':\n                return '🔄';\n            case 'alert':\n                return '⚠️';\n            default:\n                return '📍';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-20 px-4 max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Global Protest Feed\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"Real-time updates on protests and movements worldwide\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: feedItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow-md overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl\",\n                                                        children: getTypeIcon(item.type)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                        lineNumber: 88,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                className: \"text-xl font-semibold text-gray-900\",\n                                                                children: item.title\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-4 text-sm text-gray-500 mt-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCCD \",\n                                                                            item.location\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                        lineNumber: 92,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCC5 \",\n                                                                            item.date\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                        lineNumber: 93,\n                                                                        columnNumber: 25\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: [\n                                                                            \"\\uD83D\\uDC65 \",\n                                                                            item.participants.toLocaleString(),\n                                                                            \" participants\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                        lineNumber: 94,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 91,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                        lineNumber: 89,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-3 py-1 rounded-full text-xs font-medium text-white capitalize\",\n                                                style: {\n                                                    backgroundColor: getStatusColor(item.status)\n                                                },\n                                                children: item.status\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 mb-4\",\n                                        children: item.description\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mb-4\",\n                                        children: item.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 text-xs rounded-full\",\n                                                style: {\n                                                    backgroundColor: '#c8d5b9',\n                                                    color: '#4a7c59'\n                                                },\n                                                children: [\n                                                    \"#\",\n                                                    tag\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center gap-1 text-gray-600 hover:text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDC4D\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 123,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Support\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center gap-1 text-gray-600 hover:text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCAC\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Comment\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 128,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"flex items-center gap-1 text-gray-600 hover:text-gray-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"\\uD83D\\uDCE4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 131,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Share\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                                lineNumber: 132,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"px-4 py-2 text-sm rounded-md text-white transition-colors\",\n                                                style: {\n                                                    backgroundColor: '#4a7c59'\n                                                },\n                                                onMouseEnter: (e)=>{\n                                                    e.currentTarget.style.backgroundColor = '#81a989';\n                                                },\n                                                onMouseLeave: (e)=>{\n                                                    e.currentTarget.style.backgroundColor = '#4a7c59';\n                                                },\n                                                children: \"View Details\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 15\n                            }, undefined)\n                        }, item.id, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-6 py-3 text-white rounded-lg transition-colors\",\n                        style: {\n                            backgroundColor: '#4a7c59'\n                        },\n                        onMouseEnter: (e)=>{\n                            e.currentTarget.style.backgroundColor = '#81a989';\n                        },\n                        onMouseLeave: (e)=>{\n                            e.currentTarget.style.backgroundColor = '#4a7c59';\n                        },\n                        children: \"Load More Posts\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/feed.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, undefined);\n};\n_c = Feed;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Feed);\nvar _c;\n$RefreshReg$(_c, \"Feed\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/feed.tsx\n"));

/***/ })

});