"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "(pages-dir-browser)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-browser)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-browser)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\nvar _s = $RefreshSig$();\n\n\nconst Navigation = ()=>{\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isActive = (path)=>router.pathname === path;\n    const navLink = (href, label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: href,\n            className: \"text-white px-4 py-2 rounded transition-colors duration-200 \".concat(isActive(href) ? 'bg-[#81a989]' : 'hover:bg-[#c8d5b9]'),\n            children: label\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 px-4 py-3 bg-color-primary\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-white text-xl font-bold mr-auto\",\n                    children: \"Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined),\n                navLink('/', 'Home'),\n                navLink('/feed', 'Feed'),\n                navLink('/profile', 'Profile')\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navigation, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Navigation;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\nvar _c;\n$RefreshReg$(_c, \"Navigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/global/Navigation.tsx\n"));

/***/ })

});