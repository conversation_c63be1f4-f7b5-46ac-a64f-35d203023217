"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/profile",{

/***/ "(pages-dir-browser)/./pages/profile.tsx":
/*!***************************!*\
  !*** ./pages/profile.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$();\n\nconst Profile = ()=>{\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const userData = {\n        name: 'Alex Johnson',\n        username: '@alexj_activist',\n        location: 'New York, NY',\n        joinDate: 'March 2023',\n        bio: 'Human rights advocate and community organizer. Passionate about social justice and environmental protection.',\n        stats: {\n            protestsAttended: 23,\n            postsShared: 156,\n            followersCount: 2847,\n            followingCount: 1205\n        },\n        badges: [\n            {\n                name: 'Veteran Activist',\n                icon: '🏅',\n                description: 'Attended 20+ protests'\n            },\n            {\n                name: 'Community Leader',\n                icon: '👥',\n                description: 'Organized 5+ events'\n            },\n            {\n                name: 'Climate Warrior',\n                icon: '🌱',\n                description: 'Active in environmental causes'\n            }\n        ],\n        recentActivity: [\n            {\n                id: 1,\n                type: 'attended',\n                event: 'Climate Action Rally',\n                location: 'Central Park, NY',\n                date: '2024-07-10',\n                icon: '📢'\n            },\n            {\n                id: 2,\n                type: 'shared',\n                event: 'Labor Rights Update',\n                location: 'Detroit, MI',\n                date: '2024-07-08',\n                icon: '📤'\n            },\n            {\n                id: 3,\n                type: 'organized',\n                event: 'Community Food Drive',\n                location: 'Brooklyn, NY',\n                date: '2024-07-05',\n                icon: '🎯'\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-[#c8d5b9]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-20 px-4 max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md overflow-hidden mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 w-full bg-[#c8d5b9]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-16 left-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 rounded-full border-4 border-white flex items-center justify-center text-4xl font-bold text-white bg-[#4a7c59]\",\n                                        children: \"AJ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-20 pb-8 px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: userData.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: userData.username\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCCD \",\n                                                        userData.location,\n                                                        \" • Joined \",\n                                                        userData.joinDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-6 py-2 text-white rounded-lg bg-[#4a7c59] transition-colors hover:bg-[#81a989]\",\n                                            children: \"Edit Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6\",\n                                    children: userData.bio\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-6 mb-6\",\n                                    children: [\n                                        [\n                                            'Protests Attended',\n                                            userData.stats.protestsAttended\n                                        ],\n                                        [\n                                            'Posts Shared',\n                                            userData.stats.postsShared\n                                        ],\n                                        [\n                                            'Followers',\n                                            userData.stats.followersCount.toLocaleString()\n                                        ],\n                                        [\n                                            'Following',\n                                            userData.stats.followingCount.toLocaleString()\n                                        ]\n                                    ].map((param)=>{\n                                        let [label, value] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, label, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4\",\n                                            children: userData.badges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl\",\n                                                            children: badge.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: badge.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: badge.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 mb-6\",\n                    children: [\n                        'overview',\n                        'activity',\n                        'posts',\n                        'events'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-4 py-2 rounded-lg font-medium capitalize transition-colors\\n                \".concat(activeTab === tab ? 'bg-[#4a7c59] text-white' : 'bg-transparent text-[#4a7c59] border border-[#4a7c59] hover:bg-[#c8d5b9]', \"\\n              \"),\n                            onClick: ()=>setActiveTab(tab),\n                            children: tab\n                        }, tab, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Recent Activity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: userData.recentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: activity.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: [\n                                                                activity.type === 'attended' ? 'Attended' : activity.type === 'shared' ? 'Shared' : 'Organized',\n                                                                ' ',\n                                                                activity.event\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                activity.location,\n                                                                \" • \",\n                                                                activity.date\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 13\n                        }, undefined),\n                        activeTab === 'activity' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"All Activity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive activity history will be displayed here.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 13\n                        }, undefined),\n                        activeTab === 'posts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Your Posts\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Your shared posts and updates will be displayed here.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, undefined),\n                        activeTab === 'events' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Events\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Events you've organized or plan to attend will be displayed here.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Profile, \"chU/96YDf6tpLdqvgqS2ZL+A4Bo=\");\n_c = Profile;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Profile);\nvar _c;\n$RefreshReg$(_c, \"Profile\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/profile.tsx\n"));

/***/ })

});