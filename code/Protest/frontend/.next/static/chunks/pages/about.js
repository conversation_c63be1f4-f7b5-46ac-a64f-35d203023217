/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/about"],{

/***/ "(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fruhisawant%2FDesktop%2FSchool%2FSoftware%20Engineering%2Fglobal-protest-tracker%2FSotware-Engineering%2Ffrontend%2Fpages%2Fabout.tsx&page=%2Fabout!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fruhisawant%2FDesktop%2FSchool%2FSoftware%20Engineering%2Fglobal-protest-tracker%2FSotware-Engineering%2Ffrontend%2Fpages%2Fabout.tsx&page=%2Fabout! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/about\",\n      function () {\n        return __webpack_require__(/*! ./pages/about.tsx */ \"(pages-dir-browser)/./pages/about.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/about\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtY2xpZW50LXBhZ2VzLWxvYWRlci5qcz9hYnNvbHV0ZVBhZ2VQYXRoPSUyRlVzZXJzJTJGcnVoaXNhd2FudCUyRkRlc2t0b3AlMkZTY2hvb2wlMkZTb2Z0d2FyZSUyMEVuZ2luZWVyaW5nJTJGZ2xvYmFsLXByb3Rlc3QtdHJhY2tlciUyRlNvdHdhcmUtRW5naW5lZXJpbmclMkZmcm9udGVuZCUyRnBhZ2VzJTJGYWJvdXQudHN4JnBhZ2U9JTJGYWJvdXQhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsZ0VBQW1CO0FBQzFDO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAod2luZG93Ll9fTkVYVF9QID0gd2luZG93Ll9fTkVYVF9QIHx8IFtdKS5wdXNoKFtcbiAgICAgIFwiL2Fib3V0XCIsXG4gICAgICBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiByZXF1aXJlKFwiLi9wYWdlcy9hYm91dC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2Fib3V0XCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fruhisawant%2FDesktop%2FSchool%2FSoftware%20Engineering%2Fglobal-protest-tracker%2FSotware-Engineering%2Ffrontend%2Fpages%2Fabout.tsx&page=%2Fabout!\n"));

/***/ }),

/***/ "(pages-dir-browser)/./pages/about.tsx":
/*!*************************!*\
  !*** ./pages/about.tsx ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nconst About = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 max-w-4xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-center mb-8\",\n                    children: \"About Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                    lineNumber: 5,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Our Mission\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 8,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 mb-6\",\n                            children: \"Global Protest Tracker is dedicated to documenting and monitoring peaceful protests and demonstrations worldwide. We believe in the fundamental right to peaceful assembly and aim to provide transparent, accurate information about global protest movements.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"What We Do\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-blue-50 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"Track Events\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 18,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"We monitor protests, demonstrations, and peaceful assemblies across the globe, providing real-time updates and historical data.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 19,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-green-50 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"Verify Information\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"Our team verifies submitted information through multiple sources to ensure accuracy and reliability of the data we present.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 26,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-purple-50 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"Provide Context\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"We offer contextual information about protest movements, their causes, and their impact on society and policy.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 33,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-orange-50 p-6 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold mb-3\",\n                                            children: \"Support Research\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"Our platform serves as a resource for researchers, journalists, and citizens interested in understanding global protest patterns.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-8 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Our Values\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-100 rounded-full p-2 mr-4 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-blue-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 52,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"Neutrality\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: \"We maintain political neutrality and report facts without bias.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-100 rounded-full p-2 mr-4 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-green-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"Transparency\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: \"We are transparent about our sources and verification processes.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-purple-100 rounded-full p-2 mr-4 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-2 h-2 bg-purple-600 rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"Accessibility\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-700\",\n                                                    children: \"We make information freely accessible to everyone.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-2xl font-semibold mb-4\",\n                            children: \"Contact Us\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"General Inquiries\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"Media Relations\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"Data Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold mb-2\",\n                                            children: \"Technical Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/about.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, undefined);\n};\n_c = About;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (About);\nvar _c;\n$RefreshReg$(_c, \"About\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./pages/about.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["pages/_app","main"], () => (__webpack_exec__("(pages-dir-browser)/./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2FUsers%2Fruhisawant%2FDesktop%2FSchool%2FSoftware%20Engineering%2Fglobal-protest-tracker%2FSotware-Engineering%2Ffrontend%2Fpages%2Fabout.tsx&page=%2Fabout!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);