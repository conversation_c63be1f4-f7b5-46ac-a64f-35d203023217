/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/profile";
exports.ids = ["pages/profile"];
exports.modules = {

/***/ "(pages-dir-node)/./components/global/Navigation.tsx":
/*!******************************************!*\
  !*** ./components/global/Navigation.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Navigation = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const isActive = (path)=>router.pathname === path;\n    const navLink = (href, label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n            href: href,\n            className: `text-white px-4 py-2 rounded transition-colors duration-200 ${isActive(href) ? 'bg-[#81a989]' : 'hover:bg-[#c8d5b9]'}`,\n            children: label\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 9,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"fixed top-0 w-full z-50 px-4 py-3 bg-[#4a7c59]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-white text-xl font-bold mr-auto\",\n                    children: \"Global Protest Tracker\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, undefined),\n                navLink('/', 'Home'),\n                navLink('/feed', 'Feed'),\n                navLink('/profile', 'Profile')\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/components/global/Navigation.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navigation);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/global/Navigation.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./global.css":
/*!********************!*\
  !*** ./global.css ***!
  \********************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprofile&preferredRegion=&absolutePagePath=.%2Fpages%2Fprofile.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprofile&preferredRegion=&absolutePagePath=.%2Fpages%2Fprofile.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/profile.tsx */ \"(pages-dir-node)/./pages/profile.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__]);\nprivate_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/profile\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_profile_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprofile&preferredRegion=&absolutePagePath=.%2Fpages%2Fprofile.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box!=!@mui/material */ \"(pages-dir-node)/__barrel_optimize__?names=Box!=!./node_modules/@mui/material/esm/index.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/./node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_global_Navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/global/Navigation */ \"(pages-dir-node)/./components/global/Navigation.tsx\");\n/* harmony import */ var _global_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../global.css */ \"(pages-dir-node)/./global.css\");\n/* harmony import */ var _global_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_global_css__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_4__]);\n_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_4__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\nconst App = ({ Component, pageProps })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Global Protest Tracker\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"initial-scale=1.0, width=device-width\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Navigation__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_mui_material__WEBPACK_IMPORTED_MODULE_4__.Box, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                    ...pageProps\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/_app.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBb0M7QUFFUDtBQUM0QjtBQUNsQztBQUV2QixNQUFNRyxNQUFNLENBQUMsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDN0MscUJBQ0UsOERBQUNMLHdFQUFHQTs7MEJBQ0YsOERBQUNDLGtEQUFJQTs7a0NBQ0gsOERBQUNLO2tDQUFNOzs7Ozs7a0NBQ1AsOERBQUNDO3dCQUFLQyxNQUFLO3dCQUFXQyxTQUFROzs7Ozs7Ozs7Ozs7MEJBRWhDLDhEQUFDUCxxRUFBVUE7Ozs7OzBCQUNYLDhEQUFDRix3RUFBR0E7MEJBQ0YsNEVBQUNJO29CQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSWhDO0FBRUEsaUVBQWVGLEdBQUdBLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9ydWhpc2F3YW50L0Rlc2t0b3AvU2Nob29sL1NvZnR3YXJlIEVuZ2luZWVyaW5nL2dsb2JhbC1wcm90ZXN0LXRyYWNrZXIvU290d2FyZS1FbmdpbmVlcmluZy9mcm9udGVuZC9wYWdlcy9fYXBwLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBCb3ggfSBmcm9tICdAbXVpL21hdGVyaWFsJztcbmltcG9ydCB7IEFwcFByb3BzIH0gZnJvbSAnbmV4dC9hcHAnO1xuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJztcbmltcG9ydCBOYXZpZ2F0aW9uIGZyb20gJy4uL2NvbXBvbmVudHMvZ2xvYmFsL05hdmlnYXRpb24nO1xuaW1wb3J0ICcuLi9nbG9iYWwuY3NzJztcblxuY29uc3QgQXBwID0gKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpID0+IHtcbiAgcmV0dXJuIChcbiAgICA8Qm94PlxuICAgICAgPEhlYWQ+XG4gICAgICAgIDx0aXRsZT5HbG9iYWwgUHJvdGVzdCBUcmFja2VyPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cImluaXRpYWwtc2NhbGU9MS4wLCB3aWR0aD1kZXZpY2Utd2lkdGhcIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPE5hdmlnYXRpb24gLz5cbiAgICAgIDxCb3g+XG4gICAgICAgIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz5cbiAgICAgIDwvQm94PlxuICAgIDwvQm94PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgQXBwOyJdLCJuYW1lcyI6WyJCb3giLCJIZWFkIiwiTmF2aWdhdGlvbiIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsInRpdGxlIiwibWV0YSIsIm5hbWUiLCJjb250ZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/profile.tsx":
/*!***************************!*\
  !*** ./pages/profile.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Profile = ()=>{\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const userData = {\n        name: 'Alex Johnson',\n        username: '@alexj_activist',\n        location: 'New York, NY',\n        joinDate: 'March 2023',\n        bio: 'Human rights advocate and community organizer. Passionate about social justice and environmental protection.',\n        stats: {\n            protestsAttended: 23,\n            postsShared: 156,\n            followersCount: 2847,\n            followingCount: 1205\n        },\n        badges: [\n            {\n                name: 'Veteran Activist',\n                icon: '🏅',\n                description: 'Attended 20+ protests'\n            },\n            {\n                name: 'Community Leader',\n                icon: '👥',\n                description: 'Organized 5+ events'\n            },\n            {\n                name: 'Climate Warrior',\n                icon: '🌱',\n                description: 'Active in environmental causes'\n            }\n        ],\n        recentActivity: [\n            {\n                id: 1,\n                type: 'attended',\n                event: 'Climate Action Rally',\n                location: 'Central Park, NY',\n                date: '2024-07-10',\n                icon: '📢'\n            },\n            {\n                id: 2,\n                type: 'shared',\n                event: 'Labor Rights Update',\n                location: 'Detroit, MI',\n                date: '2024-07-08',\n                icon: '📤'\n            },\n            {\n                id: 3,\n                type: 'organized',\n                event: 'Community Food Drive',\n                location: 'Brooklyn, NY',\n                date: '2024-07-05',\n                icon: '🎯'\n            }\n        ]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"pt-20 px-4 max-w-6xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md overflow-hidden mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-48 w-full bg-[#c8d5b9]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-16 left-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-32 h-32 rounded-full border-4 border-white flex items-center justify-center text-4xl font-bold text-white bg-[#4a7c59]\",\n                                        children: \"AJ\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-20 pb-8 px-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-start mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold text-gray-900\",\n                                                    children: userData.name\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: userData.username\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 mt-1\",\n                                                    children: [\n                                                        \"\\uD83D\\uDCCD \",\n                                                        userData.location,\n                                                        \" • Joined \",\n                                                        userData.joinDate\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"px-6 py-2 text-white rounded-lg bg-[#4a7c59] transition-colors hover:bg-[#81a989]\",\n                                            children: \"Edit Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-6\",\n                                    children: userData.bio\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-6 mb-6\",\n                                    children: [\n                                        [\n                                            'Protests Attended',\n                                            userData.stats.protestsAttended\n                                        ],\n                                        [\n                                            'Posts Shared',\n                                            userData.stats.postsShared\n                                        ],\n                                        [\n                                            'Followers',\n                                            userData.stats.followersCount.toLocaleString()\n                                        ],\n                                        [\n                                            'Following',\n                                            userData.stats.followingCount.toLocaleString()\n                                        ]\n                                    ].map(([label, value])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-gray-900\",\n                                                    children: value\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-[#6b8f71]\",\n                                                    children: label\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, label, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold text-gray-900 mb-3\",\n                                            children: \"Achievements\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-4\",\n                                            children: userData.badges.map((badge, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 bg-gray-50 px-3 py-2 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xl\",\n                                                            children: badge.icon\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium text-gray-900\",\n                                                                    children: badge.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-xs text-gray-600\",\n                                                                    children: badge.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                                    lineNumber: 107,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2 mb-6\",\n                    children: [\n                        'overview',\n                        'activity',\n                        'posts',\n                        'events'\n                    ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: `px-4 py-2 rounded-lg font-medium capitalize transition-colors ${activeTab === tab ? 'bg-[#4a7c59] text-white' : 'bg-transparent text-[#4a7c59] border border-[#4a7c59] hover:bg-[#81a989] hover:text-white'}`,\n                            onClick: ()=>setActiveTab(tab),\n                            children: tab\n                        }, tab, false, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-md p-6\",\n                    children: [\n                        activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Recent Activity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: userData.recentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4 p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-2xl\",\n                                                    children: activity.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: [\n                                                                activity.type === 'attended' ? 'Attended' : activity.type === 'shared' ? 'Shared' : 'Organized',\n                                                                ' ',\n                                                                activity.event\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 143,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                activity.location,\n                                                                \" • \",\n                                                                activity.date\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, undefined),\n                        activeTab === 'activity' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"All Activity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Comprehensive activity history will be displayed here.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 162,\n                            columnNumber: 13\n                        }, undefined),\n                        activeTab === 'posts' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Your Posts\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Your shared posts and updates will be displayed here.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 13\n                        }, undefined),\n                        activeTab === 'events' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                                    children: \"Events\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Events you've organized or plan to attend will be displayed here.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n            lineNumber: 53,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/School/Software Engineering/global-protest-tracker/Sotware-Engineering/frontend/pages/profile.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Profile);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/profile.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Box!=!./node_modules/@mui/material/esm/index.js":
/*!*********************************************************************************!*\
  !*** __barrel_optimize__?names=Box!=!./node_modules/@mui/material/esm/index.js ***!
  \*********************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Box: () => (/* reexport safe */ _Box_index_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _Box_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./Box/index.js */ "(pages-dir-node)/./node_modules/@mui/material/esm/Box/index.js");
var __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_Box_index_js__WEBPACK_IMPORTED_MODULE_0__]);
_Box_index_js__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];


__webpack_async_result__();
} catch(e) { __webpack_async_result__(e); } });

/***/ }),

/***/ "@mui/system":
/*!******************************!*\
  !*** external "@mui/system" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system");;

/***/ }),

/***/ "@mui/system/InitColorSchemeScript":
/*!****************************************************!*\
  !*** external "@mui/system/InitColorSchemeScript" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/InitColorSchemeScript");;

/***/ }),

/***/ "@mui/system/colorManipulator":
/*!***********************************************!*\
  !*** external "@mui/system/colorManipulator" ***!
  \***********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/colorManipulator");;

/***/ }),

/***/ "@mui/system/createBreakpoints":
/*!************************************************!*\
  !*** external "@mui/system/createBreakpoints" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/createBreakpoints");;

/***/ }),

/***/ "@mui/system/createStyled":
/*!*******************************************!*\
  !*** external "@mui/system/createStyled" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/createStyled");;

/***/ }),

/***/ "@mui/system/createTheme":
/*!******************************************!*\
  !*** external "@mui/system/createTheme" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/createTheme");;

/***/ }),

/***/ "@mui/system/cssVars":
/*!**************************************!*\
  !*** external "@mui/system/cssVars" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/cssVars");;

/***/ }),

/***/ "@mui/system/spacing":
/*!**************************************!*\
  !*** external "@mui/system/spacing" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/spacing");;

/***/ }),

/***/ "@mui/system/styleFunctionSx":
/*!**********************************************!*\
  !*** external "@mui/system/styleFunctionSx" ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/styleFunctionSx");;

/***/ }),

/***/ "@mui/system/useThemeProps":
/*!********************************************!*\
  !*** external "@mui/system/useThemeProps" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/system/useThemeProps");;

/***/ }),

/***/ "@mui/utils/ClassNameGenerator":
/*!************************************************!*\
  !*** external "@mui/utils/ClassNameGenerator" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/utils/ClassNameGenerator");;

/***/ }),

/***/ "@mui/utils/deepmerge":
/*!***************************************!*\
  !*** external "@mui/utils/deepmerge" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/utils/deepmerge");;

/***/ }),

/***/ "@mui/utils/formatMuiErrorMessage":
/*!***************************************************!*\
  !*** external "@mui/utils/formatMuiErrorMessage" ***!
  \***************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/utils/formatMuiErrorMessage");;

/***/ }),

/***/ "@mui/utils/generateUtilityClass":
/*!**************************************************!*\
  !*** external "@mui/utils/generateUtilityClass" ***!
  \**************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/utils/generateUtilityClass");;

/***/ }),

/***/ "@mui/utils/generateUtilityClasses":
/*!****************************************************!*\
  !*** external "@mui/utils/generateUtilityClasses" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@mui/utils/generateUtilityClasses");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/@mui"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprofile&preferredRegion=&absolutePagePath=.%2Fpages%2Fprofile.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();