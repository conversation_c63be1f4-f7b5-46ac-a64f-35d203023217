import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'
import AdminDashboard from '../../frontend/pages/admin/dashboard'

const mockPendingSubmissions = [
  {
    submission_id: '507f1f77bcf86cd799439030',
    title: 'University Tuition Protest',
    cause: 'Education Rights',
    location: 'University Campus, Atlanta',
    submitted_by: '<EMAIL>',
    submitted_at: '2024-03-15T10:30:00Z',
    status: 'pending_moderation',
    expected_turnout: 1500
  },
  {
    submission_id: '507f1f77bcf86cd799439031',
    title: 'Healthcare Workers Strike',
    cause: 'Labor Rights',
    location: 'City Hospital, Atlanta',
    submitted_by: '<EMAIL>',
    submitted_at: '2024-03-15T14:20:00Z',
    status: 'pending_moderation',
    expected_turnout: 800
  }
]

global.fetch = jest.fn()

describe('Scenario 3: Admin Logs In and Approves a Protest Submission', () => {
  beforeEach(() => {
    fetch.mockClear()
  })

  test('admin login with elevated credentials', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        user: {
          role: 'admin',
          email: '<EMAIL>',
          permissions: ['moderate_content', 'manage_users', 'view_analytics']
        },
        token: 'admin_token'
      })
    })

    render(<AdminDashboard />)
    
    const emailInput = screen.getByTestId('admin-login-email')
    const passwordInput = screen.getByTestId('admin-login-password')
    const loginButton = screen.getByTestId('admin-login-button')
    
    await userEvent.type(emailInput, '<EMAIL>')
    await userEvent.type(passwordInput, 'admin_secure_pass')
    await userEvent.click(loginButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin_secure_pass'
        })
      })
    })
    
    expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Welcome, <EMAIL>')).toBeInTheDocument()
  })

  test('displays pending submissions list', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockPendingSubmissions
    })

    render(<AdminDashboard />)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/admin/pending-submissions', {
        headers: { 'Authorization': 'Bearer admin_token' }
      })
    })
    
    expect(screen.getByTestId('pending-submissions-list')).toBeInTheDocument()
    expect(screen.getByText('University Tuition Protest')).toBeInTheDocument()
    expect(screen.getByText('Healthcare Workers Strike')).toBeInTheDocument()
    expect(screen.getByText('2 pending submissions')).toBeInTheDocument()
  })

  test('views detailed submission information', async () => {
    const submissionDetails = {
      submission_id: '507f1f77bcf86cd799439030',
      title: 'University Tuition Protest',
      cause: 'Education Rights',
      location: 'University Campus, Atlanta',
      date: '2024-04-01',
      expected_turnout: 1500,
      description: 'Students protesting increased tuition fees',
      photo_urls: ['https://storage.example.com/protest_photo_123.jpg'],
      submitted_by: {
        user_id: '507f1f77bcf86cd799439021',
        email: '<EMAIL>',
        verification_status: 'verified'
      },
      geolocation: { lat: 33.7490, lng: -84.3880 },
      submitted_at: '2024-03-15T10:30:00Z'
    }

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => submissionDetails
    })

    render(<AdminDashboard />)
    
    const viewDetailsButton = screen.getByTestId('view-details-507f1f77bcf86cd799439030')
    await userEvent.click(viewDetailsButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/admin/submissions/507f1f77bcf86cd799439030', {
        headers: { 'Authorization': 'Bearer admin_token' }
      })
    })
    
    expect(screen.getByTestId('submission-details-modal')).toBeInTheDocument()
    expect(screen.getByText('University Tuition Protest')).toBeInTheDocument()
    expect(screen.getByText('Education Rights')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    expect(screen.getByText('verified')).toBeInTheDocument()
    expect(screen.getByText('1500')).toBeInTheDocument()
  })

  test('verifies submission details and location', async () => {
    render(<AdminDashboard />)
    
    const viewDetailsButton = screen.getByTestId('view-details-507f1f77bcf86cd799439030')
    await userEvent.click(viewDetailsButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('submission-verification-checklist')).toBeInTheDocument()
    })
    
    const locationVerified = screen.getByTestId('location-verified-checkbox')
    const detailsVerified = screen.getByTestId('details-verified-checkbox')
    const userVerified = screen.getByTestId('user-verified-checkbox')
    
    await userEvent.click(locationVerified)
    await userEvent.click(detailsVerified)
    await userEvent.click(userVerified)
    
    expect(locationVerified).toBeChecked()
    expect(detailsVerified).toBeChecked()
    expect(userVerified).toBeChecked()
  })

  test('approves protest submission', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        protest_id: '507f1f77bcf86cd799439040',
        status: 'approved'
      })
    })

    render(<AdminDashboard />)
    
    const approveButton = screen.getByTestId('approve-submission-507f1f77bcf86cd799439030')
    const adminNotes = screen.getByTestId('admin-notes-input')
    
    await userEvent.type(adminNotes, 'Verified location and details. Approved for public display.')
    await userEvent.click(approveButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/admin/approve-submission', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin_token'
        },
        body: JSON.stringify({
          submission_id: '507f1f77bcf86cd799439030',
          admin_notes: 'Verified location and details. Approved for public display.',
          approved_by: 'admin_user_id'
        })
      })
    })
    
    expect(screen.getByText('Submission approved successfully')).toBeInTheDocument()
  })

  test('checks updated public feed after approval', async () => {
    const publicFeed = [
      {
        _id: '507f1f77bcf86cd799439040',
        title: 'University Tuition Protest',
        cause: 'Education Rights',
        location: 'University Campus, Atlanta',
        date: '2024-04-01',
        size: 1500,
        status: 'approved',
        approved_at: '2024-03-15T16:45:00Z',
        approved_by: '<EMAIL>'
      }
    ]

    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => publicFeed
    })

    render(<AdminDashboard />)
    
    const viewPublicFeedButton = screen.getByTestId('view-public-feed')
    await userEvent.click(viewPublicFeedButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/protests/public-feed', {
        headers: { 'Authorization': 'Bearer admin_token' }
      })
    })
    
    expect(screen.getByTestId('public-feed-modal')).toBeInTheDocument()
    expect(screen.getByText('University Tuition Protest')).toBeInTheDocument()
    expect(screen.getByText('approved')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  test('logs admin moderation action', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ logged: true })
    })

    render(<AdminDashboard />)
    
    const logActionButton = screen.getByTestId('log-action-button')
    await userEvent.click(logActionButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/admin/log-action', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer admin_token'
        },
        body: JSON.stringify({
          action: 'approve_submission',
          submission_id: '507f1f77bcf86cd799439030',
          admin_id: 'admin_user_id',
          timestamp: expect.any(String),
          notes: 'Verified and approved for public display'
        })
      })
    })
    
    expect(screen.getByText('Action logged successfully')).toBeInTheDocument()
  })
})
