import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'
import HomePage from '../../frontend/pages/index'

const mockProtests = [
  {
    id: 1,
    title: 'Climate Action March',
    location: 'Atlanta, Georgia',
    cause: 'Environmental Justice',
    size: 5000,
    media_coverage: 'High',
    date: '2024-03-15',
    coordinates: { lat: 33.7490, lng: -84.3880 }
  },
  {
    id: 2,
    title: 'Workers Rights Rally',
    location: 'Atlanta, Georgia',
    cause: 'Labor Rights',
    size: 2000,
    media_coverage: 'Medium',
    date: '2024-03-20',
    coordinates: { lat: 33.7490, lng: -84.3880 }
  }
]

global.fetch = jest.fn()

describe('Scenario 1: Concerned Citizen Browsing and Saving Protests', () => {
  beforeEach(() => {
    fetch.mockClear()
  })

  test('displays homepage with interactive map', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProtests
    })

    render(<HomePage />)
    
    expect(screen.getByText('Global Protest Tracker')).toBeInTheDocument()
    expect(screen.getByTestId('interactive-map')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(screen.getByText('Climate Action March')).toBeInTheDocument()
    })
  })

  test('filters protests by Atlanta location', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProtests.filter(p => p.location.includes('Atlanta'))
    })

    render(<HomePage />)
    
    const locationFilter = screen.getByTestId('location-filter')
    await userEvent.type(locationFilter, 'Atlanta, Georgia')
    
    const filterButton = screen.getByTestId('apply-filter-button')
    await userEvent.click(filterButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/protests?location=Atlanta,Georgia')
    })
  })

  test('displays protest details popup when clicked', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProtests
    })

    render(<HomePage />)
    
    await waitFor(() => {
      const protestMarker = screen.getByTestId('protest-marker-1')
      fireEvent.click(protestMarker)
    })
    
    expect(screen.getByTestId('protest-details-popup')).toBeInTheDocument()
    expect(screen.getByText('Climate Action March')).toBeInTheDocument()
    expect(screen.getByText('Environmental Justice')).toBeInTheDocument()
    expect(screen.getByText('5000')).toBeInTheDocument()
    expect(screen.getByText('High')).toBeInTheDocument()
  })

  test('saves protest to user saved list', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProtests
    })
    
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    })

    render(<HomePage />)
    
    await waitFor(() => {
      const protestMarker = screen.getByTestId('protest-marker-1')
      fireEvent.click(protestMarker)
    })
    
    const saveButton = screen.getByTestId('save-protest-button')
    await userEvent.click(saveButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/users/saved-protests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer mock_token'
        },
        body: JSON.stringify({ protest_id: 1 })
      })
    })
    
    expect(screen.getByText('Protest saved successfully')).toBeInTheDocument()
  })

  test('views saved protests list', async () => {
    const savedProtests = [mockProtests[0]]
    
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => savedProtests
    })

    render(<HomePage />)
    
    const savedProtestsButton = screen.getByTestId('view-saved-protests')
    await userEvent.click(savedProtestsButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/users/saved-protests', {
        headers: { 'Authorization': 'Bearer mock_token' }
      })
    })
    
    expect(screen.getByTestId('saved-protests-modal')).toBeInTheDocument()
    expect(screen.getByText('Climate Action March')).toBeInTheDocument()
  })

  test('browses second protest and views details', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => mockProtests
    })

    render(<HomePage />)
    
    await waitFor(() => {
      const secondProtestMarker = screen.getByTestId('protest-marker-2')
      fireEvent.click(secondProtestMarker)
    })
    
    expect(screen.getByTestId('protest-details-popup')).toBeInTheDocument()
    expect(screen.getByText('Workers Rights Rally')).toBeInTheDocument()
    expect(screen.getByText('Labor Rights')).toBeInTheDocument()
    expect(screen.getByText('2000')).toBeInTheDocument()
  })

  test('user logout functionality', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    })

    render(<HomePage />)
    
    const userMenu = screen.getByTestId('user-menu')
    await userEvent.click(userMenu)
    
    const logoutButton = screen.getByTestId('logout-button')
    await userEvent.click(logoutButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/auth/logout', {
        method: 'POST',
        headers: { 'Authorization': 'Bearer mock_token' }
      })
    })
    
    expect(screen.getByText('Logged out successfully')).toBeInTheDocument()
  })
})
