import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { jest } from '@jest/globals'
import SubmitProtestPage from '../../frontend/pages/submit-protest'

global.fetch = jest.fn()

describe('Scenario 2: Activist Posts a New Protest Report', () => {
  beforeEach(() => {
    fetch.mockClear()
  })

  test('activist login process', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        user: { role: 'activist', email: '<EMAIL>' },
        token: 'activist_token'
      })
    })

    render(<SubmitProtestPage />)
    
    const emailInput = screen.getByTestId('login-email')
    const passwordInput = screen.getByTestId('login-password')
    const loginButton = screen.getByTestId('login-button')
    
    await userEvent.type(emailInput, '<EMAIL>')
    await userEvent.type(passwordInput, 'password123')
    await userEvent.click(loginButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'password123'
        })
      })
    })
    
    expect(screen.getByText('Welcome, <EMAIL>')).toBeInTheDocument()
  })

  test('displays protest submission form', async () => {
    render(<SubmitProtestPage />)
    
    expect(screen.getByTestId('protest-submission-form')).toBeInTheDocument()
    expect(screen.getByLabelText('Protest Title')).toBeInTheDocument()
    expect(screen.getByLabelText('Cause/Category')).toBeInTheDocument()
    expect(screen.getByLabelText('Location')).toBeInTheDocument()
    expect(screen.getByLabelText('Date')).toBeInTheDocument()
    expect(screen.getByLabelText('Expected Turnout')).toBeInTheDocument()
    expect(screen.getByLabelText('Description')).toBeInTheDocument()
  })

  test('fills out protest submission form', async () => {
    render(<SubmitProtestPage />)
    
    const titleInput = screen.getByLabelText('Protest Title')
    const causeSelect = screen.getByLabelText('Cause/Category')
    const locationInput = screen.getByLabelText('Location')
    const dateInput = screen.getByLabelText('Date')
    const turnoutInput = screen.getByLabelText('Expected Turnout')
    const descriptionInput = screen.getByLabelText('Description')
    
    await userEvent.type(titleInput, 'University Tuition Protest')
    await userEvent.selectOptions(causeSelect, 'Education Rights')
    await userEvent.type(locationInput, 'University Campus, Atlanta')
    await userEvent.type(dateInput, '2024-04-01')
    await userEvent.type(turnoutInput, '1500')
    await userEvent.type(descriptionInput, 'Students protesting increased tuition fees')
    
    expect(titleInput).toHaveValue('University Tuition Protest')
    expect(causeSelect).toHaveValue('Education Rights')
    expect(locationInput).toHaveValue('University Campus, Atlanta')
    expect(dateInput).toHaveValue('2024-04-01')
    expect(turnoutInput).toHaveValue('1500')
    expect(descriptionInput).toHaveValue('Students protesting increased tuition fees')
  })

  test('uploads protest photo', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        photo_url: 'https://storage.example.com/protest_photo_123.jpg'
      })
    })

    render(<SubmitProtestPage />)
    
    const fileInput = screen.getByTestId('photo-upload')
    const file = new File(['fake image data'], 'protest.jpg', { type: 'image/jpeg' })
    
    await userEvent.upload(fileInput, file)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/user-content/upload-photo', expect.objectContaining({
        method: 'POST',
        headers: { 'Authorization': 'Bearer activist_token' }
      }))
    })
    
    expect(screen.getByText('Photo uploaded successfully')).toBeInTheDocument()
    expect(screen.getByTestId('uploaded-photo-preview')).toBeInTheDocument()
  })

  test('validates form data before submission', async () => {
    render(<SubmitProtestPage />)
    
    const submitButton = screen.getByTestId('submit-protest-button')
    await userEvent.click(submitButton)
    
    expect(screen.getByText('Title is required')).toBeInTheDocument()
    expect(screen.getByText('Location is required')).toBeInTheDocument()
    expect(screen.getByText('Date is required')).toBeInTheDocument()
  })

  test('submits protest report successfully', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        submission_id: '507f1f77bcf86cd799439030',
        status: 'pending_moderation'
      })
    })

    render(<SubmitProtestPage />)
    
    await userEvent.type(screen.getByLabelText('Protest Title'), 'University Tuition Protest')
    await userEvent.selectOptions(screen.getByLabelText('Cause/Category'), 'Education Rights')
    await userEvent.type(screen.getByLabelText('Location'), 'University Campus, Atlanta')
    await userEvent.type(screen.getByLabelText('Date'), '2024-04-01')
    await userEvent.type(screen.getByLabelText('Expected Turnout'), '1500')
    await userEvent.type(screen.getByLabelText('Description'), 'Students protesting increased tuition fees')
    
    const submitButton = screen.getByTestId('submit-protest-button')
    await userEvent.click(submitButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/user-content/submit-protest', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer activist_token'
        },
        body: JSON.stringify({
          title: 'University Tuition Protest',
          cause: 'Education Rights',
          location: 'University Campus, Atlanta',
          date: '2024-04-01',
          expected_turnout: 1500,
          description: 'Students protesting increased tuition fees'
        })
      })
    })
  })

  test('displays submission confirmation message', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        submission_id: '507f1f77bcf86cd799439030',
        status: 'pending_moderation'
      })
    })

    render(<SubmitProtestPage />)
    
    await userEvent.type(screen.getByLabelText('Protest Title'), 'University Tuition Protest')
    await userEvent.selectOptions(screen.getByLabelText('Cause/Category'), 'Education Rights')
    await userEvent.type(screen.getByLabelText('Location'), 'University Campus, Atlanta')
    await userEvent.type(screen.getByLabelText('Date'), '2024-04-01')
    await userEvent.type(screen.getByLabelText('Expected Turnout'), '1500')
    
    const submitButton = screen.getByTestId('submit-protest-button')
    await userEvent.click(submitButton)
    
    await waitFor(() => {
      expect(screen.getByTestId('confirmation-message')).toBeInTheDocument()
      expect(screen.getByText('Report submitted successfully')).toBeInTheDocument()
      expect(screen.getByText('Status: Pending Moderation')).toBeInTheDocument()
      expect(screen.getByText('Estimated review time: 24-48 hours')).toBeInTheDocument()
    })
  })

  test('activist logout after submission', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true })
    })

    render(<SubmitProtestPage />)
    
    const logoutButton = screen.getByTestId('logout-button')
    await userEvent.click(logoutButton)
    
    await waitFor(() => {
      expect(fetch).toHaveBeenCalledWith('/api/auth/logout', {
        method: 'POST',
        headers: { 'Authorization': 'Bearer activist_token' }
      })
    })
    
    expect(screen.getByText('Logged out successfully')).toBeInTheDocument()
  })
})
